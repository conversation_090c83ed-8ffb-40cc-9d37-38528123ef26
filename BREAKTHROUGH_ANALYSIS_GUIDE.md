# 股票突破回调分析指南

## 功能概述

这个模块可以帮你：
1. **获取股票历史数据** - 支持1-5年的历史数据
2. **ZigZag峰谷分析** - 自动识别股价的峰和谷
3. **突破模式识别** - 找出突破前期高点的股票
4. **回调分析** - 分析突破后的回调幅度
5. **筛选优质标的** - 找出有突破且回调合理的股票

## 快速开始

### 1. 快速测试
```bash
python quick_breakthrough_test.py
```
选择选项1，系统会自动分析5只热门股票的突破回调模式。

### 2. 自定义分析
```bash
python quick_breakthrough_test.py
```
选择选项2，可以输入自己想分析的股票代码。

### 3. 完整分析
```python
from breakthrough_analysis import BreakthroughAnalyzer
from batch_data_loader import BatchDataConfig
from tushare_config import get_tushare_config

# 配置
tushare_config = get_tushare_config()
data_config = BatchDataConfig(
    tushare_token=tushare_config.get_token(),
    data_source='tushare',
    max_workers=1,
    request_delay=1.0
)

# 分析
analyzer = BreakthroughAnalyzer(data_config)
results = analyzer.analyze_stocks(['000001', '600036'], years=3)
```

## 分析指标说明

### 突破回调模式定义
1. **突破强度** - 突破前期高点的幅度百分比
2. **回调深度** - 突破后回调的幅度百分比
3. **当前位置** - 当前价格相对于突破点的位置
   - `strong`: 接近突破高点
   - `moderate`: 中等位置
   - `weak`: 接近回调低点

### 评级标准
- **⭐⭐⭐ 优秀**: 突破强度>20%，回调<30%，位置强势
- **⭐⭐ 良好**: 突破强度>10%，回调<40%
- **⭐ 一般**: 其他情况

## 使用示例

### 示例1: 分析特定股票
```python
# 分析银行股的突破回调
bank_stocks = ['000001', '600036', '600000', '601166']
results = analyzer.analyze_stocks(bank_stocks, years=3)

# 查看结果
for stock in results['stocks_with_breakthrough']:
    print(f"{stock['symbol']}: 突破{stock['breakthrough_strength']:.1f}%")
```

### 示例2: 批量筛选
```python
# 分析更多股票
large_list = ['000001', '000002', '600036', '600519', '000858', 
              '002415', '600276', '000725', '002594', '600887']
results = analyzer.analyze_stocks(large_list, years=5)

# 按突破强度排序
breakthrough_stocks = results['stocks_with_breakthrough']
for stock in breakthrough_stocks[:3]:  # 前3名
    print(f"推荐: {stock['symbol']}")
```

## 参数调整

### 数据获取参数
```python
data_config = BatchDataConfig(
    max_workers=1,        # 并发数，避免速率限制
    request_delay=1.0,    # 请求间隔，秒
    data_source='tushare' # 数据源选择
)
```

### ZigZag参数
```python
zigzag_calculator = ZigZagCalculator(
    atr_period=14,        # ATR周期
    atr_multiplier=2.0    # ATR倍数，影响敏感度
)
```

## 注意事项

### 1. 速率限制
- 使用保守的请求设置避免API限制
- 建议 `max_workers=1`, `request_delay=1.0`
- 如遇速率限制，程序会自动重试

### 2. 数据源选择
- **Tushare**: 数据质量高，需要token
- **AKShare**: 免费使用，可能不稳定

### 3. 分析时间
- 1年数据: 约1-2分钟/股
- 3年数据: 约2-3分钟/股  
- 5年数据: 约3-5分钟/股

### 4. 结果解读
- 突破回调是技术分析概念，不构成投资建议
- 建议结合基本面分析
- 注意市场环境和行业趋势

## 常见问题

### Q: 为什么某些股票没有发现突破模式？
A: 可能原因：
- 股票处于震荡或下跌趋势
- ZigZag点数量不足（需要至少4个确认点）
- 突破幅度不够显著
- 回调过深（超过50%）

### Q: 如何提高分析准确性？
A: 建议：
- 增加分析时间范围（3-5年）
- 调整ZigZag参数适应不同股票
- 结合成交量分析
- 考虑行业和市场环境

### Q: 程序运行很慢怎么办？
A: 优化方法：
- 减少股票数量
- 缩短时间范围
- 检查网络连接
- 使用缓存功能

## 扩展功能

可以基于这个框架扩展：
1. **成交量确认** - 突破时成交量放大
2. **行业对比** - 同行业股票对比分析
3. **技术指标结合** - 结合RSI、MACD等指标
4. **回测功能** - 历史突破回调的成功率统计
5. **实时监控** - 定期扫描新的突破机会

## 联系支持

如有问题或建议，请检查：
1. 网络连接是否正常
2. 数据源配置是否正确
3. 股票代码格式是否正确
4. 查看日志输出了解详细错误信息
