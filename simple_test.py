"""
简化测试脚本
测试核心功能，不依赖数据库
"""

import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tushare_basic():
    """测试Tushare基础功能"""
    print("="*60)
    print("测试Tushare基础功能")
    print("="*60)
    
    try:
        from tushare_config import get_tushare_config, setup_tushare_token
        
        config = get_tushare_config()
        
        if not config.is_configured():
            print("Tushare未配置，尝试使用AKShare...")
            return test_akshare_basic()
        
        print(f"Tushare已配置: {config.get_token()[:10]}...")
        
        # 测试基本数据获取
        from data_loader import get_stock_data
        
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=10)).strftime('%Y%m%d')
        
        print(f"测试获取平安银行10天数据...")
        df = get_stock_data('000001', start_date, end_date, config.get_token())
        
        if not df.empty:
            print(f"✅ 成功获取 {len(df)} 条数据")
            print("最新数据:")
            print(df.tail(1)[['date', 'open', 'high', 'low', 'close']].to_string())
            return True
        else:
            print("❌ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ Tushare测试失败: {e}")
        print("尝试使用AKShare...")
        return test_akshare_basic()

def test_akshare_basic():
    """测试AKShare基础功能"""
    print("\n" + "="*60)
    print("测试AKShare基础功能")
    print("="*60)
    
    try:
        import akshare as ak
        
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=10)).strftime('%Y%m%d')
        
        print(f"测试获取平安银行10天数据...")
        df = ak.stock_zh_a_hist(
            symbol='000001',
            period="daily",
            start_date=start_date,
            end_date=end_date,
            adjust="qfq"
        )
        
        if not df.empty:
            print(f"✅ 成功获取 {len(df)} 条数据")
            print("最新数据:")
            print(df.tail(1)[['日期', '开盘', '最高', '最低', '收盘']].to_string())
            return True
        else:
            print("❌ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ AKShare测试失败: {e}")
        return False

def test_batch_data_loading():
    """测试批量数据获取"""
    print("\n" + "="*60)
    print("测试批量数据获取")
    print("="*60)
    
    try:
        from batch_data_loader import BatchDataLoader, BatchDataConfig
        from tushare_config import get_tushare_config
        
        # 检查Tushare配置
        tushare_config = get_tushare_config()
        
        if tushare_config.is_configured():
            print("使用Tushare数据源...")
            data_config = BatchDataConfig(
                data_source='tushare',
                tushare_token=tushare_config.get_token(),
                max_workers=2,
                request_delay=0.3
            )
        else:
            print("使用AKShare数据源...")
            data_config = BatchDataConfig(
                data_source='akshare',
                max_workers=3,
                request_delay=0.2
            )
        
        loader = BatchDataLoader(data_config)
        
        # 测试少量股票
        test_symbols = ['000001', '000002']
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=10)).strftime('%Y%m%d')
        
        print(f"批量获取 {len(test_symbols)} 只股票数据...")
        data_dict = loader.get_batch_stock_data(test_symbols, start_date, end_date)
        
        print(f"✅ 成功获取 {len(data_dict)} 只股票数据:")
        for symbol, df in data_dict.items():
            print(f"  {symbol}: {len(df)} 条记录")
        
        return len(data_dict) > 0
        
    except Exception as e:
        print(f"❌ 批量数据获取测试失败: {e}")
        return False

def test_zigzag_analysis():
    """测试ZigZag分析"""
    print("\n" + "="*60)
    print("测试ZigZag分析")
    print("="*60)
    
    try:
        # 使用模拟数据测试ZigZag
        from data_models import StockData
        from zigzag import ZigZagState, update_state
        from decimal import Decimal
        import random
        
        # 创建模拟数据
        random.seed(42)
        base_date = datetime.now() - timedelta(days=30)
        stock_data_list = []
        base_price = 30.0
        
        for i in range(30):
            price_change = random.uniform(-0.03, 0.03)
            close_price = base_price * (1 + price_change)
            
            stock_data = StockData(
                symbol="TEST001",
                date=base_date + timedelta(days=i),
                open=Decimal(f"{base_price:.2f}"),
                high=Decimal(f"{close_price * 1.02:.2f}"),
                low=Decimal(f"{close_price * 0.98:.2f}"),
                close=Decimal(f"{close_price:.2f}"),
                volume=1000000
            )
            stock_data_list.append(stock_data)
            base_price = close_price
        
        print(f"创建了 {len(stock_data_list)} 条模拟数据")
        
        # ZigZag分析
        state = ZigZagState(symbol="TEST001")
        
        for data in stock_data_list:
            state = update_state(state, data, stock_data_list)
        
        print(f"✅ ZigZag分析完成:")
        print(f"  确认拐点: {len(state.confirmed_points)}")
        print(f"  当前趋势: {state.trend}")
        print(f"  趋势持续: {state.bars_in_trend} 天")
        
        if state.confirmed_points:
            print("  最近拐点:")
            for point in state.confirmed_points[-3:]:
                print(f"    {point.date} {point.point_type} {point.price}")
        
        return True
        
    except Exception as e:
        print(f"❌ ZigZag分析测试失败: {e}")
        return False

def test_stock_pool_mock():
    """测试股票池模拟功能"""
    print("\n" + "="*60)
    print("测试股票池模拟功能")
    print("="*60)
    
    try:
        from stock_pool import StockInfo
        
        # 创建模拟股票池
        mock_stocks = [
            StockInfo(
                symbol='000001',
                name='平安银行',
                market_cap=2500.0,
                price=12.50,
                pe_ratio=8.5,
                pb_ratio=1.2,
                turnover_rate=2.1,
                avg_volume=15000000,
                industry='银行',
                list_date='1991-04-03',
                is_st=False
            ),
            StockInfo(
                symbol='000002',
                name='万科A',
                market_cap=1800.0,
                price=16.20,
                pe_ratio=12.3,
                pb_ratio=1.8,
                turnover_rate=1.5,
                avg_volume=8000000,
                industry='房地产',
                list_date='1991-01-29',
                is_st=False
            )
        ]
        
        print(f"✅ 创建模拟股票池: {len(mock_stocks)} 只股票")
        for stock in mock_stocks:
            print(f"  {stock.symbol} {stock.name} 市值:{stock.market_cap}亿")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票池模拟测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("股票分析系统简化测试")
    print("测试核心功能，不依赖数据库")
    
    results = {}
    
    # 1. 测试数据获取
    print("\n🔍 测试数据获取功能...")
    results['data_source'] = test_tushare_basic()
    
    # 2. 测试批量数据获取
    if results['data_source']:
        print("\n🔍 测试批量数据获取...")
        results['batch_data'] = test_batch_data_loading()
    else:
        results['batch_data'] = False
    
    # 3. 测试ZigZag分析
    print("\n🔍 测试ZigZag分析...")
    results['zigzag'] = test_zigzag_analysis()
    
    # 4. 测试股票池模拟
    print("\n🔍 测试股票池模拟...")
    results['stock_pool'] = test_stock_pool_mock()
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15s}: {status}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count >= 2:  # 至少ZigZag和股票池功能正常
        print("🎉 核心功能正常！")
        print("\n建议:")
        if not results.get('data_source'):
            print("- 配置数据源以获取真实数据")
            print("- Tushare: python main.py --mode config")
            print("- 或使用: --data-source akshare")
        print("- 安装duckdb以启用数据库功能: pip install duckdb")
    else:
        print("⚠️  核心功能存在问题，请检查环境配置")

if __name__ == "__main__":
    main()
