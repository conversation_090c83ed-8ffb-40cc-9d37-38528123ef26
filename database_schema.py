"""
DuckDB数据库表结构定义和管理
"""

import duckdb
import pandas as pd
from typing import List, Dict, Optional, Any
from datetime import datetime
import logging
import json
from pathlib import Path

from data_models import StockData
from zigzag import ZigZagState, ZigZagPoint

logger = logging.getLogger(__name__)

class DatabaseManager:
    """DuckDB数据库管理器"""
    
    def __init__(self, db_path: str = "stock_analysis.duckdb"):
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.conn = duckdb.connect(self.db_path)
            logger.info(f"连接到数据库: {self.db_path}")
            self._create_tables()
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
            logger.info("数据库连接已断开")
    
    def _create_tables(self):
        """创建所有必要的表"""
        
        # 1. 股票基本信息表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_info (
                symbol VARCHAR PRIMARY KEY,
                name VARCHAR NOT NULL,
                market_cap DECIMAL(15,2),  -- 市值（亿元）
                industry VARCHAR,
                list_date DATE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 2. 股票历史数据表（分区表，按日期分区）
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_daily_data (
                id BIGINT PRIMARY KEY,
                symbol VARCHAR NOT NULL,
                date DATE NOT NULL,
                open DECIMAL(10,3) NOT NULL,
                high DECIMAL(10,3) NOT NULL,
                low DECIMAL(10,3) NOT NULL,
                close DECIMAL(10,3) NOT NULL,
                volume BIGINT NOT NULL,
                amount DECIMAL(15,2),  -- 成交额
                turnover_rate DECIMAL(8,4),  -- 换手率
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date)
            )
        """)
        
        # 3. ZigZag状态表（存储每只股票的ZigZag计算状态）
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS zigzag_states (
                symbol VARCHAR PRIMARY KEY,
                atr_period INTEGER NOT NULL,
                atr_multiplier DECIMAL(8,4) NOT NULL,
                min_price_move DECIMAL(10,3),
                min_trend_bars INTEGER,
                current_trend VARCHAR,  -- 'UP'/'DOWN'/NULL
                bars_in_trend INTEGER,
                extreme_high DECIMAL(10,3),
                extreme_high_date DATE,
                extreme_low DECIMAL(10,3),
                extreme_low_date DATE,
                atr_value DECIMAL(10,6),
                state_json TEXT,  -- 完整状态的JSON序列化
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 4. ZigZag拐点表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS zigzag_points (
                id BIGINT PRIMARY KEY,
                symbol VARCHAR NOT NULL,
                date DATE NOT NULL,
                point_type VARCHAR NOT NULL,  -- 'HIGH'/'LOW'
                price DECIMAL(10,3) NOT NULL,
                atr_value DECIMAL(10,6),
                threshold_used DECIMAL(10,6),
                source VARCHAR DEFAULT 'zigzag',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date, point_type)
            )
        """)
        
        # 5. 技术指标表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS technical_indicators (
                id BIGINT PRIMARY KEY,
                symbol VARCHAR NOT NULL,
                date DATE NOT NULL,
                indicator_name VARCHAR NOT NULL,  -- 指标名称：'MACD', 'BB', 'RSI'等
                value1 DECIMAL(15,6),  -- 主值
                value2 DECIMAL(15,6),  -- 辅助值1
                value3 DECIMAL(15,6),  -- 辅助值2
                metadata TEXT,  -- JSON格式的额外信息
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date, indicator_name)
            )
        """)
        
        # 6. 策略信号表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS strategy_signals (
                id BIGINT PRIMARY KEY,
                symbol VARCHAR NOT NULL,
                date DATE NOT NULL,
                strategy_name VARCHAR NOT NULL,
                signal_type VARCHAR NOT NULL,  -- 'BUY'/'SELL'/'HOLD'
                signal_strength DECIMAL(5,2),  -- 信号强度 0-100
                price DECIMAL(10,3),  -- 信号价格
                confidence DECIMAL(5,2),  -- 置信度 0-100
                metadata TEXT,  -- JSON格式的策略参数和额外信息
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 7. 回测结果表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS backtest_results (
                id BIGINT PRIMARY KEY,
                strategy_name VARCHAR NOT NULL,
                symbol VARCHAR,  -- NULL表示组合回测
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                total_return DECIMAL(8,4),  -- 总收益率
                annual_return DECIMAL(8,4),  -- 年化收益率
                max_drawdown DECIMAL(8,4),  -- 最大回撤
                sharpe_ratio DECIMAL(8,4),  -- 夏普比率
                win_rate DECIMAL(5,2),  -- 胜率
                trade_count INTEGER,  -- 交易次数
                config_json TEXT,  -- 策略配置JSON
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建索引以提高查询性能
        self._create_indexes()
        
        logger.info("数据库表结构创建完成")
    
    def _create_indexes(self):
        """创建索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_stock_daily_symbol_date ON stock_daily_data(symbol, date)",
            "CREATE INDEX IF NOT EXISTS idx_stock_daily_date ON stock_daily_data(date)",
            "CREATE INDEX IF NOT EXISTS idx_zigzag_points_symbol_date ON zigzag_points(symbol, date)",
            "CREATE INDEX IF NOT EXISTS idx_zigzag_points_symbol_type ON zigzag_points(symbol, point_type)",
            "CREATE INDEX IF NOT EXISTS idx_technical_indicators_symbol_date ON technical_indicators(symbol, date)",
            "CREATE INDEX IF NOT EXISTS idx_technical_indicators_name ON technical_indicators(indicator_name)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_signals_symbol_date ON strategy_signals(symbol, date)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_signals_strategy ON strategy_signals(strategy_name)",
            "CREATE INDEX IF NOT EXISTS idx_backtest_results_strategy ON backtest_results(strategy_name)"
        ]
        
        for index_sql in indexes:
            try:
                self.conn.execute(index_sql)
            except Exception as e:
                logger.warning(f"创建索引失败: {e}")
    
    def insert_stock_data_batch(self, stock_data_dict: Dict[str, List[StockData]]):
        """批量插入股票历史数据"""
        logger.info(f"开始批量插入 {len(stock_data_dict)} 只股票的历史数据")
        
        total_records = 0
        
        for symbol, stock_data_list in stock_data_dict.items():
            try:
                # 准备数据
                records = []
                for i, data in enumerate(stock_data_list):
                    record = {
                        'id': hash(f"{symbol}_{data.date}") % (2**63),  # 生成唯一ID
                        'symbol': symbol,
                        'date': data.date.strftime('%Y-%m-%d'),
                        'open': float(data.open),
                        'high': float(data.high),
                        'low': float(data.low),
                        'close': float(data.close),
                        'volume': data.volume
                    }
                    records.append(record)
                
                # 转换为DataFrame并插入
                df = pd.DataFrame(records)
                self.conn.execute("INSERT OR REPLACE INTO stock_daily_data SELECT * FROM df")
                
                total_records += len(records)
                logger.debug(f"插入 {symbol}: {len(records)} 条记录")
                
            except Exception as e:
                logger.error(f"插入 {symbol} 数据失败: {e}")
        
        logger.info(f"批量插入完成: 总计 {total_records} 条记录")
    
    def insert_zigzag_state(self, state: ZigZagState):
        """插入或更新ZigZag状态"""
        try:
            # 序列化状态为JSON
            state_json = json.dumps(state.to_dict(), ensure_ascii=False)
            
            record = {
                'symbol': state.symbol,
                'atr_period': state.atr_period,
                'atr_multiplier': float(state.atr_multiplier),
                'min_price_move': float(state.min_price_move) if state.min_price_move else None,
                'min_trend_bars': state.min_trend_bars,
                'current_trend': state.trend,
                'bars_in_trend': state.bars_in_trend,
                'extreme_high': float(state.extreme_high) if state.extreme_high else None,
                'extreme_high_date': state.extreme_high_date,
                'extreme_low': float(state.extreme_low) if state.extreme_low else None,
                'extreme_low_date': state.extreme_low_date,
                'atr_value': float(state.atr) if state.atr else None,
                'state_json': state_json,
                'updated_at': datetime.now().isoformat()
            }
            
            df = pd.DataFrame([record])
            self.conn.execute("INSERT OR REPLACE INTO zigzag_states SELECT * FROM df")
            
        except Exception as e:
            logger.error(f"插入ZigZag状态失败 {state.symbol}: {e}")
    
    def insert_zigzag_points_batch(self, symbol: str, points: List[ZigZagPoint]):
        """批量插入ZigZag拐点"""
        if not points:
            return
            
        try:
            records = []
            for point in points:
                record = {
                    'id': hash(f"{symbol}_{point.date}_{point.point_type}") % (2**63),
                    'symbol': symbol,
                    'date': point.date,
                    'point_type': point.point_type,
                    'price': float(point.price),
                    'source': point.source
                }
                records.append(record)
            
            df = pd.DataFrame(records)
            self.conn.execute("INSERT OR REPLACE INTO zigzag_points SELECT * FROM df")
            
            logger.debug(f"插入 {symbol} ZigZag拐点: {len(records)} 个")
            
        except Exception as e:
            logger.error(f"插入ZigZag拐点失败 {symbol}: {e}")
    
    def get_zigzag_state(self, symbol: str) -> Optional[ZigZagState]:
        """获取ZigZag状态"""
        try:
            result = self.conn.execute(
                "SELECT state_json FROM zigzag_states WHERE symbol = ?", 
                [symbol]
            ).fetchone()
            
            if result:
                state_dict = json.loads(result[0])
                return ZigZagState.from_dict(state_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"获取ZigZag状态失败 {symbol}: {e}")
            return None
    
    def get_stock_data_range(self, symbol: str, start_date: str, end_date: str) -> List[StockData]:
        """获取指定时间范围的股票数据"""
        try:
            df = self.conn.execute("""
                SELECT symbol, date, open, high, low, close, volume
                FROM stock_daily_data 
                WHERE symbol = ? AND date BETWEEN ? AND ?
                ORDER BY date
            """, [symbol, start_date, end_date]).df()
            
            stock_data_list = []
            for _, row in df.iterrows():
                stock_data = StockData(
                    symbol=row['symbol'],
                    date=pd.to_datetime(row['date']).to_pydatetime(),
                    open=Decimal(str(row['open'])),
                    high=Decimal(str(row['high'])),
                    low=Decimal(str(row['low'])),
                    close=Decimal(str(row['close'])),
                    volume=int(row['volume'])
                )
                stock_data_list.append(stock_data)
            
            return stock_data_list
            
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return []
    
    def get_zigzag_points(self, symbol: str, start_date: str = None, end_date: str = None) -> List[ZigZagPoint]:
        """获取ZigZag拐点"""
        try:
            sql = "SELECT date, point_type, price, source FROM zigzag_points WHERE symbol = ?"
            params = [symbol]
            
            if start_date:
                sql += " AND date >= ?"
                params.append(start_date)
            if end_date:
                sql += " AND date <= ?"
                params.append(end_date)
                
            sql += " ORDER BY date"
            
            df = self.conn.execute(sql, params).df()
            
            points = []
            for _, row in df.iterrows():
                point = ZigZagPoint(
                    date=row['date'],
                    point_type=row['point_type'],
                    price=Decimal(str(row['price'])),
                    source=row['source']
                )
                points.append(point)
            
            return points
            
        except Exception as e:
            logger.error(f"获取ZigZag拐点失败 {symbol}: {e}")
            return []
    
    def insert_strategy_signal(self, symbol: str, date: str, strategy_name: str, 
                             signal_type: str, signal_strength: float, 
                             price: float, confidence: float = 100.0, 
                             metadata: Dict = None):
        """插入策略信号"""
        try:
            record = {
                'id': hash(f"{symbol}_{date}_{strategy_name}_{signal_type}") % (2**63),
                'symbol': symbol,
                'date': date,
                'strategy_name': strategy_name,
                'signal_type': signal_type,
                'signal_strength': signal_strength,
                'price': price,
                'confidence': confidence,
                'metadata': json.dumps(metadata or {}, ensure_ascii=False)
            }
            
            df = pd.DataFrame([record])
            self.conn.execute("INSERT OR REPLACE INTO strategy_signals SELECT * FROM df")
            
        except Exception as e:
            logger.error(f"插入策略信号失败: {e}")
    
    def get_strategy_signals(self, symbol: str = None, strategy_name: str = None, 
                           start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """查询策略信号"""
        try:
            sql = "SELECT * FROM strategy_signals WHERE 1=1"
            params = []
            
            if symbol:
                sql += " AND symbol = ?"
                params.append(symbol)
            if strategy_name:
                sql += " AND strategy_name = ?"
                params.append(strategy_name)
            if start_date:
                sql += " AND date >= ?"
                params.append(start_date)
            if end_date:
                sql += " AND date <= ?"
                params.append(end_date)
                
            sql += " ORDER BY date DESC"
            
            return self.conn.execute(sql, params).df()
            
        except Exception as e:
            logger.error(f"查询策略信号失败: {e}")
            return pd.DataFrame()
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {}
            
            # 股票数量
            result = self.conn.execute("SELECT COUNT(*) FROM stock_info").fetchone()
            stats['stock_count'] = result[0] if result else 0
            
            # 历史数据记录数
            result = self.conn.execute("SELECT COUNT(*) FROM stock_daily_data").fetchone()
            stats['daily_data_count'] = result[0] if result else 0
            
            # ZigZag拐点数量
            result = self.conn.execute("SELECT COUNT(*) FROM zigzag_points").fetchone()
            stats['zigzag_points_count'] = result[0] if result else 0
            
            # 策略信号数量
            result = self.conn.execute("SELECT COUNT(*) FROM strategy_signals").fetchone()
            stats['strategy_signals_count'] = result[0] if result else 0
            
            # 数据日期范围
            result = self.conn.execute(
                "SELECT MIN(date), MAX(date) FROM stock_daily_data"
            ).fetchone()
            if result and result[0]:
                stats['data_date_range'] = {
                    'start': str(result[0]),
                    'end': str(result[1])
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')
            
            # 清理旧的历史数据
            result = self.conn.execute(
                "DELETE FROM stock_daily_data WHERE date < ?", 
                [cutoff_date]
            )
            
            # 清理旧的拐点数据
            self.conn.execute(
                "DELETE FROM zigzag_points WHERE date < ?", 
                [cutoff_date]
            )
            
            # 清理旧的策略信号
            self.conn.execute(
                "DELETE FROM strategy_signals WHERE date < ?", 
                [cutoff_date]
            )
            
            logger.info(f"清理 {cutoff_date} 之前的旧数据完成")
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")

# 全局数据库实例
_db_manager = None

def get_database() -> DatabaseManager:
    """获取数据库管理器单例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
        _db_manager.connect()
    return _db_manager

def close_database():
    """关闭数据库连接"""
    global _db_manager
    if _db_manager:
        _db_manager.disconnect()
        _db_manager = None

if __name__ == "__main__":
    # 测试数据库创建
    print("测试数据库创建...")
    
    db = DatabaseManager("test_stock.duckdb")
    db.connect()
    
    # 显示统计信息
    stats = db.get_database_stats()
    print("数据库统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    db.disconnect()
    print("数据库测试完成")
