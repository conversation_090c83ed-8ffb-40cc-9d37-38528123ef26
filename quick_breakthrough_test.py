"""
快速突破分析测试
简化版本，用于快速测试突破回调功能
"""

from breakthrough_analysis import BreakthroughAnalyzer
from batch_data_loader import BatchDataConfig
from tushare_config import get_tushare_config

def quick_test():
    """快速测试突破分析功能"""
    
    # 测试股票列表（你可以修改）
    test_stocks = [
        '000001',  # 平安银行
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415'   # 海康威视
    ]
    
    print("🚀 开始快速突破分析测试...")
    print(f"测试股票: {', '.join(test_stocks)}")
    
    # 配置数据源（保守设置避免速率限制）
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,  # 单线程避免速率限制
        request_delay=1.0  # 1秒间隔
    )
    
    print(f"数据源: {data_config.data_source}")
    print("请耐心等待，正在获取数据...")
    
    # 创建分析器并执行分析
    analyzer = BreakthroughAnalyzer(data_config)
    results = analyzer.analyze_stocks(test_stocks, years=3)  # 3年数据，速度更快
    
    # 显示简化结果
    print("\n" + "="*60)
    print("📊 分析结果摘要")
    print("="*60)
    
    breakthrough_stocks = results['stocks_with_breakthrough']
    
    if breakthrough_stocks:
        print(f"✅ 发现 {len(breakthrough_stocks)} 只有突破回调模式的股票:")
        
        for stock in breakthrough_stocks:
            symbol = stock['symbol']
            strength = stock['breakthrough_strength']
            pullback = stock['pullback_depth']
            position = stock['current_position']
            
            # 简单的评级
            if strength > 20 and pullback < 30 and position in ['strong', 'moderate']:
                rating = "⭐⭐⭐ 优秀"
            elif strength > 10 and pullback < 40:
                rating = "⭐⭐ 良好"
            else:
                rating = "⭐ 一般"
            
            print(f"\n📈 {symbol} {rating}")
            print(f"   突破强度: {strength:.1f}%")
            print(f"   回调深度: {pullback:.1f}%")
            print(f"   当前位置: {position}")
            
            # 显示详细分析
            if symbol in results['detailed_results']:
                detail = results['detailed_results'][symbol]
                if 'peak_valley_analysis' in detail:
                    pv = detail['peak_valley_analysis']
                    if 'avg_peak_valley_amplitude' in pv:
                        print(f"   平均波幅: {pv['avg_peak_valley_amplitude']:.1f}%")
    else:
        print("❌ 未发现明显的突破回调模式")
        print("\n可能的原因:")
        print("- 市场处于震荡期")
        print("- 选择的股票没有明显突破")
        print("- 可以尝试更多股票或调整时间范围")
    
    # 显示每只股票的基本信息
    print(f"\n📋 详细分析结果:")
    for symbol in test_stocks:
        if symbol in results['detailed_results']:
            detail = results['detailed_results'][symbol]
            has_pattern = "✅" if detail['has_breakthrough_pullback'] else "❌"
            print(f"{has_pattern} {symbol}: {detail.get('reason', '已分析')}")
    
    print(f"\n总计: {results['successful_analysis']}/{results['total_stocks']} 只股票分析成功")
    
    return results

def custom_analysis():
    """自定义股票分析"""
    print("\n" + "="*60)
    print("🎯 自定义股票突破分析")
    print("="*60)
    
    # 让用户输入股票代码
    print("请输入要分析的股票代码（用空格分隔）:")
    print("例如: 000001 600036 600519")
    user_input = input("股票代码: ").strip()
    
    if not user_input:
        print("未输入股票代码，使用默认列表")
        return quick_test()
    
    custom_stocks = user_input.split()
    print(f"将分析: {', '.join(custom_stocks)}")
    
    # 询问分析年数
    try:
        years = int(input("分析年数 (1-5，默认3): ") or "3")
        years = max(1, min(5, years))
    except:
        years = 3
    
    print(f"分析 {years} 年数据...")
    
    # 配置和执行分析
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.0
    )
    
    analyzer = BreakthroughAnalyzer(data_config)
    results = analyzer.analyze_stocks(custom_stocks, years=years)
    
    # 显示结果（复用quick_test的显示逻辑）
    breakthrough_stocks = results['stocks_with_breakthrough']
    
    print("\n" + "="*60)
    print("📊 自定义分析结果")
    print("="*60)
    
    if breakthrough_stocks:
        print(f"✅ 发现 {len(breakthrough_stocks)} 只有突破回调模式的股票:")
        for stock in breakthrough_stocks:
            print(f"📈 {stock['symbol']}: 突破{stock['breakthrough_strength']:.1f}%, 回调{stock['pullback_depth']:.1f}%")
    else:
        print("❌ 未发现突破回调模式")
    
    return results

def main():
    """主菜单"""
    print("🎯 股票突破回调分析工具")
    print("="*40)
    print("1. 快速测试 (5只热门股票)")
    print("2. 自定义分析")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        return quick_test()
    elif choice == "2":
        return custom_analysis()
    elif choice == "3":
        print("再见！")
        return None
    else:
        print("无效选择，执行快速测试...")
        return quick_test()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        print("请检查网络连接和数据源配置")
