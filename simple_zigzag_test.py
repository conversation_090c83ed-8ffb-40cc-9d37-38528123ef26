"""
简单的ZigZag测试
用于调试ZigZag分析问题
"""

from breakthrough_analysis import BreakthroughAnalyzer
from batch_data_loader import BatchDataConfig
from tushare_config import get_tushare_config
from datetime import datetime, timedelta

def test_single_stock():
    """测试单只股票的ZigZag分析"""
    print("🔍 测试单只股票ZigZag分析")
    
    # 配置
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare',
        max_workers=1,
        request_delay=1.0
    )
    
    # 创建分析器
    analyzer = BreakthroughAnalyzer(data_config)
    
    # 获取测试数据
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')  # 3个月数据
    
    print(f"获取平安银行(000001) {start_date} 到 {end_date} 的数据...")
    
    try:
        # 获取数据
        df = analyzer.data_loader.get_single_stock_data('000001', start_date, end_date)
        if df is None or df.empty:
            print("❌ 数据获取失败")
            return
        
        print(f"✅ 数据获取成功: {df.shape}")
        
        # 转换数据
        stock_data_list = analyzer._convert_to_stock_data(df, '000001')
        print(f"✅ 数据转换成功: {len(stock_data_list)} 条记录")
        
        # 测试ZigZag分析
        print("开始ZigZag分析...")
        zigzag_state = analyzer._analyze_zigzag(stock_data_list)
        
        if zigzag_state:
            print(f"✅ ZigZag分析成功")
            print(f"确认的ZigZag点数量: {len(zigzag_state.confirmed_points)}")
            
            # 显示前几个ZigZag点
            for i, point in enumerate(zigzag_state.confirmed_points[:5]):
                print(f"  {i+1}. {point.date.strftime('%Y-%m-%d')} {point.point_type} {point.price}")
        else:
            print("❌ ZigZag分析失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_stock()
