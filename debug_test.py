"""
调试测试脚本
用于诊断系统运行问题
"""

import logging
import traceback
from datetime import datetime, timedelta

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_imports():
    """测试所有模块导入"""
    print("="*60)
    print("测试模块导入")
    print("="*60)
    
    try:
        print("导入基础模块...")
        import pandas as pd
        print("✅ pandas")
        
        from datetime import datetime, timedelta
        print("✅ datetime")
        
        from decimal import Decimal
        print("✅ decimal")
        
        print("\n导入项目模块...")
        from data_models import StockData
        print("✅ data_models")
        
        from tushare_config import get_tushare_config
        print("✅ tushare_config")
        
        from batch_data_loader import BatchDataLoader, BatchDataConfig
        print("✅ batch_data_loader")
        
        from stock_pool import StockPoolSelector, StockPoolConfig
        print("✅ stock_pool")
        
        from database_schema import DatabaseManager
        print("✅ database_schema")
        
        from zigzag import BatchZigZagProcessor
        print("✅ zigzag")
        
        from batch_zigzag_processor import IntegratedStockAnalyzer
        print("✅ batch_zigzag_processor")
        
        print("\n✅ 所有模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_tushare_config():
    """测试Tushare配置"""
    print("\n" + "="*60)
    print("测试Tushare配置")
    print("="*60)
    
    try:
        from tushare_config import get_tushare_config
        
        config = get_tushare_config()
        
        if config.is_configured():
            print(f"✅ Tushare已配置: {config.get_token()[:10]}...")
            return True, config.get_token()
        else:
            print("❌ Tushare未配置")
            return False, None
            
    except Exception as e:
        print(f"❌ Tushare配置测试失败: {e}")
        traceback.print_exc()
        return False, None

def test_data_config():
    """测试数据配置"""
    print("\n" + "="*60)
    print("测试数据配置")
    print("="*60)
    
    try:
        from batch_data_loader import BatchDataConfig
        
        # 测试默认配置
        config1 = BatchDataConfig()
        print(f"✅ 默认配置: {config1.data_source}")
        
        # 测试Tushare配置
        config2 = BatchDataConfig(
            data_source='tushare',
            tushare_token='test_token',
            max_workers=2,
            request_delay=0.3
        )
        print(f"✅ Tushare配置: {config2.data_source}")
        
        return True, config2
        
    except Exception as e:
        print(f"❌ 数据配置测试失败: {e}")
        traceback.print_exc()
        return False, None

def test_stock_pool_basic():
    """测试股票池基础功能"""
    print("\n" + "="*60)
    print("测试股票池基础功能")
    print("="*60)
    
    try:
        from stock_pool import StockPoolSelector, StockPoolConfig
        
        # 创建配置
        config = StockPoolConfig(
            min_market_cap=100.0,
            max_market_cap=2000.0,
            min_price=10.0,
            max_price=100.0
        )
        print("✅ 股票池配置创建成功")
        
        # 创建选择器
        selector = StockPoolSelector(config)
        print("✅ 股票池选择器创建成功")
        
        return True, selector
        
    except Exception as e:
        print(f"❌ 股票池基础功能测试失败: {e}")
        traceback.print_exc()
        return False, None

def test_database_connection():
    """测试数据库连接"""
    print("\n" + "="*60)
    print("测试数据库连接")
    print("="*60)
    
    try:
        from database_schema import DatabaseManager
        
        # 创建数据库管理器
        db_manager = DatabaseManager("test_debug.duckdb")
        print("✅ 数据库管理器创建成功")
        
        # 测试连接
        db_manager.connect()
        print("✅ 数据库连接成功")
        
        # 测试统计
        stats = db_manager.get_database_stats()
        print(f"✅ 数据库统计: {stats}")
        
        # 关闭连接
        db_manager.disconnect()
        print("✅ 数据库连接关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        traceback.print_exc()
        return False

def test_integrated_analyzer():
    """测试集成分析器"""
    print("\n" + "="*60)
    print("测试集成分析器")
    print("="*60)
    
    try:
        from batch_zigzag_processor import IntegratedStockAnalyzer
        from batch_data_loader import BatchDataConfig
        from stock_pool import StockPoolConfig
        
        # 创建配置
        data_config = BatchDataConfig(
            data_source='akshare',  # 使用AKShare避免token问题
            max_workers=2,
            request_delay=0.5
        )
        
        pool_config = StockPoolConfig(
            min_market_cap=100.0,
            max_market_cap=2000.0
        )
        
        print("✅ 配置创建成功")
        
        # 创建分析器
        analyzer = IntegratedStockAnalyzer(
            pool_config=pool_config,
            data_config=data_config,
            db_path="test_debug.duckdb"
        )
        print("✅ 集成分析器创建成功")
        
        # 测试初始化
        analyzer.initialize_system()
        print("✅ 系统初始化成功")
        
        # 关闭系统
        analyzer.shutdown_system()
        print("✅ 系统关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成分析器测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("股票分析系统调试测试")
    print("本测试用于诊断系统运行问题")
    
    results = {}
    
    # 1. 测试模块导入
    results['imports'] = test_imports()
    
    if not results['imports']:
        print("\n❌ 模块导入失败，无法继续测试")
        return
    
    # 2. 测试Tushare配置
    results['tushare'], token = test_tushare_config()
    
    # 3. 测试数据配置
    results['data_config'], data_config = test_data_config()
    
    # 4. 测试股票池基础功能
    results['stock_pool'], selector = test_stock_pool_basic()
    
    # 5. 测试数据库连接
    results['database'] = test_database_connection()
    
    # 6. 测试集成分析器
    results['analyzer'] = test_integrated_analyzer()
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15s}: {status}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！系统应该可以正常运行")
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        
        if not results.get('tushare'):
            print("\n建议:")
            print("1. 配置Tushare token: python main.py --mode config")
            print("2. 或使用AKShare: --data-source akshare")

if __name__ == "__main__":
    main()
