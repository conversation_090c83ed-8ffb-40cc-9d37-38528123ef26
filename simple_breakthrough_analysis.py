"""
简化版突破回调分析
避免复杂的类型转换问题
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd
import json
import os

from batch_data_loader import BatchDataLoader, BatchDataConfig
from data_models import StockData
from tushare_config import get_tushare_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleBreakthroughAnalyzer:
    """简化版突破回调分析器"""
    
    def __init__(self, data_config: BatchDataConfig):
        self.data_config = data_config
        self.data_loader = BatchDataLoader(data_config)
    
    def analyze_stocks(self, stock_symbols: List[str], years: int = 3) -> Dict:
        """分析股票列表的突破回调模式"""
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y%m%d')
        
        print(f"开始分析 {len(stock_symbols)} 只股票的 {years} 年数据...")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 批量获取数据
        stock_data_dict = self.data_loader.get_batch_stock_data(
            stock_symbols, start_date, end_date
        )
        
        results = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'time_range': f"{start_date} 到 {end_date}",
            'total_stocks': len(stock_symbols),
            'successful_analysis': 0,
            'stocks_with_breakthrough': [],
            'detailed_results': {}
        }
        
        # 分析每只股票
        for symbol in stock_symbols:
            try:
                df = stock_data_dict.get(symbol)
                if df is None or df.empty:
                    logger.warning(f"股票 {symbol} 数据为空，跳过分析")
                    continue
                
                # 转换为StockData格式
                stock_data_list = self._convert_to_stock_data(df, symbol)
                
                # 简化的突破分析
                breakthrough_analysis = self._simple_breakthrough_analysis(stock_data_list)
                
                results['detailed_results'][symbol] = breakthrough_analysis
                results['successful_analysis'] += 1
                
                # 如果发现突破回调模式，加入结果列表
                if breakthrough_analysis['has_breakthrough']:
                    results['stocks_with_breakthrough'].append({
                        'symbol': symbol,
                        'breakthrough_strength': breakthrough_analysis['breakthrough_strength'],
                        'current_position': breakthrough_analysis['current_position'],
                        'price_change_30d': breakthrough_analysis['price_change_30d']
                    })
                
                print(f"✅ {symbol} 分析完成")
                
            except Exception as e:
                logger.error(f"分析股票 {symbol} 失败: {e}")
                continue
        
        # 按突破强度排序
        results['stocks_with_breakthrough'].sort(
            key=lambda x: x['breakthrough_strength'], reverse=True
        )
        
        return results
    
    def _convert_to_stock_data(self, df: pd.DataFrame, symbol: str) -> List[StockData]:
        """将DataFrame转换为StockData列表"""
        stock_data_list = []
        
        for _, row in df.iterrows():
            try:
                # 处理不同数据源的列名
                if 'date' in df.columns:
                    date = pd.to_datetime(row['date'])
                    open_price = float(row['open'])
                    high_price = float(row['high'])
                    low_price = float(row['low'])
                    close_price = float(row['close'])
                    volume = int(row['volume'])
                else:
                    continue
                
                stock_data = StockData(
                    symbol=symbol,
                    date=date,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume
                )
                stock_data_list.append(stock_data)
                
            except Exception as e:
                logger.warning(f"转换数据行失败: {e}")
                continue
        
        return sorted(stock_data_list, key=lambda x: x.date)
    
    def _simple_breakthrough_analysis(self, stock_data_list: List[StockData]) -> Dict:
        """简化的突破分析"""
        analysis = {
            'has_breakthrough': False,
            'breakthrough_strength': 0.0,
            'current_position': 'unknown',
            'price_change_30d': 0.0,
            'high_low_analysis': {}
        }
        
        if len(stock_data_list) < 60:  # 至少需要60天数据
            analysis['reason'] = '数据不足'
            return analysis
        
        # 获取价格数据
        prices = [float(data.close) for data in stock_data_list]
        highs = [float(data.high) for data in stock_data_list]
        lows = [float(data.low) for data in stock_data_list]
        
        current_price = prices[-1]
        
        # 计算30天价格变化
        if len(prices) >= 30:
            price_30d_ago = prices[-30]
            analysis['price_change_30d'] = (current_price - price_30d_ago) / price_30d_ago * 100
        
        # 寻找最近6个月的高低点
        recent_period = min(120, len(prices))  # 最近120天或全部数据
        recent_highs = highs[-recent_period:]
        recent_lows = lows[-recent_period:]
        recent_prices = prices[-recent_period:]
        
        # 找到最高点和最低点
        max_high = max(recent_highs)
        min_low = min(recent_lows)
        max_high_idx = recent_highs.index(max_high)
        min_low_idx = recent_lows.index(min_low)
        
        # 分析突破模式
        # 1. 检查是否接近历史高点
        distance_from_high = (max_high - current_price) / max_high * 100
        
        # 2. 检查是否从低点有显著上涨
        if min_low_idx < len(recent_prices) - 10:  # 低点不是最近10天
            rise_from_low = (current_price - min_low) / min_low * 100
        else:
            rise_from_low = 0
        
        # 3. 判断突破条件
        breakthrough_conditions = []
        
        if distance_from_high < 10:  # 距离高点不超过10%
            breakthrough_conditions.append("接近历史高点")
            
        if rise_from_low > 20:  # 从低点上涨超过20%
            breakthrough_conditions.append(f"从低点上涨{rise_from_low:.1f}%")
            
        if analysis['price_change_30d'] > 10:  # 30天涨幅超过10%
            breakthrough_conditions.append(f"30天涨幅{analysis['price_change_30d']:.1f}%")
        
        # 计算突破强度
        breakthrough_strength = 0
        if distance_from_high < 5:
            breakthrough_strength += 30
        elif distance_from_high < 10:
            breakthrough_strength += 20
            
        if rise_from_low > 30:
            breakthrough_strength += 40
        elif rise_from_low > 20:
            breakthrough_strength += 30
        elif rise_from_low > 10:
            breakthrough_strength += 20
            
        if analysis['price_change_30d'] > 15:
            breakthrough_strength += 30
        elif analysis['price_change_30d'] > 10:
            breakthrough_strength += 20
        elif analysis['price_change_30d'] > 5:
            breakthrough_strength += 10
        
        # 判断当前位置
        if distance_from_high < 5:
            position = 'strong'
        elif distance_from_high < 15:
            position = 'moderate'
        else:
            position = 'weak'
        
        # 更新分析结果
        if breakthrough_strength > 50 and len(breakthrough_conditions) >= 2:
            analysis['has_breakthrough'] = True
            analysis['breakthrough_strength'] = breakthrough_strength
            analysis['current_position'] = position
            analysis['breakthrough_reasons'] = breakthrough_conditions
        
        # 高低点分析
        analysis['high_low_analysis'] = {
            'max_high': max_high,
            'min_low': min_low,
            'current_price': current_price,
            'distance_from_high_pct': distance_from_high,
            'rise_from_low_pct': rise_from_low
        }
        
        return analysis

    def generate_detailed_report(self, results: Dict, output_file: str = None) -> str:
        """生成详尽的专业分析报告"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"breakthrough_analysis_report_{timestamp}.md"

        report_lines = []

        # 报告标题和概述
        report_lines.extend([
            "# 股票突破回调分析报告",
            "",
            f"**生成时间**: {results['analysis_date']}",
            f"**分析期间**: {results['time_range']}",
            f"**分析股票数量**: {results['total_stocks']}",
            f"**成功分析**: {results['successful_analysis']}",
            f"**发现突破模式**: {len(results['stocks_with_breakthrough'])} 只",
            "",
            "---",
            ""
        ])

        # 执行摘要
        report_lines.extend([
            "## 📊 执行摘要",
            "",
            self._generate_executive_summary(results),
            "",
            "---",
            ""
        ])

        # 突破股票详细分析
        if results['stocks_with_breakthrough']:
            report_lines.extend([
                "## 🎯 突破股票详细分析",
                "",
                self._generate_breakthrough_details(results),
                "",
                "---",
                ""
            ])

        # 全部股票概览
        report_lines.extend([
            "## 📋 全部股票分析概览",
            "",
            self._generate_all_stocks_overview(results),
            "",
            "---",
            ""
        ])

        # 技术分析说明
        report_lines.extend([
            "## 📈 技术分析方法说明",
            "",
            self._generate_methodology_section(),
            "",
            "---",
            ""
        ])

        # 风险提示和建议
        report_lines.extend([
            "## ⚠️ 风险提示与投资建议",
            "",
            self._generate_risk_disclaimer(),
            "",
            "---",
            ""
        ])

        # 附录
        report_lines.extend([
            "## 📎 附录",
            "",
            self._generate_appendix(results),
            ""
        ])

        # 写入文件
        report_content = "\n".join(report_lines)

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📄 详细报告已生成: {output_file}")
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")

        return report_content

    def _generate_executive_summary(self, results: Dict) -> str:
        """生成执行摘要"""
        breakthrough_count = len(results['stocks_with_breakthrough'])
        total_count = results['successful_analysis']

        if breakthrough_count == 0:
            summary = f"""
本次分析共覆盖 {total_count} 只股票，**未发现明显的突破回调模式**。

**主要发现**:
- 市场可能处于震荡或调整阶段
- 大部分股票未表现出强势突破特征
- 建议继续观察或扩大分析范围

**建议操作**:
- 保持观望，等待更明确的突破信号
- 关注市场整体趋势变化
- 可考虑分析更多股票或调整时间周期
"""
        else:
            avg_strength = sum(s['breakthrough_strength'] for s in results['stocks_with_breakthrough']) / breakthrough_count
            strong_count = sum(1 for s in results['stocks_with_breakthrough'] if s['breakthrough_strength'] > 70)

            summary = f"""
本次分析共覆盖 {total_count} 只股票，发现 **{breakthrough_count} 只股票** 表现出突破回调模式。

**核心发现**:
- 突破股票占比: {breakthrough_count/total_count*100:.1f}%
- 平均突破强度: {avg_strength:.1f} 分
- 强势突破股票: {strong_count} 只 (强度>70分)

**投资机会**:
- {breakthrough_count} 只股票显示技术性突破特征
- 建议重点关注突破强度较高的标的
- 结合基本面分析确定最终投资决策
"""

        return summary.strip()

    def _generate_breakthrough_details(self, results: Dict) -> str:
        """生成突破股票详细分析"""
        details = []

        for i, stock in enumerate(results['stocks_with_breakthrough'], 1):
            symbol = stock['symbol']
            strength = stock['breakthrough_strength']
            position = stock['current_position']
            change_30d = stock['price_change_30d']

            # 获取详细数据
            stock_detail = results['detailed_results'].get(symbol, {})
            high_low = stock_detail.get('high_low_analysis', {})

            # 评级
            if strength >= 80:
                rating = "⭐⭐⭐⭐⭐ 强烈推荐"
                rating_color = "🟢"
            elif strength >= 70:
                rating = "⭐⭐⭐⭐ 推荐"
                rating_color = "🟢"
            elif strength >= 60:
                rating = "⭐⭐⭐ 关注"
                rating_color = "🟡"
            else:
                rating = "⭐⭐ 观望"
                rating_color = "🟡"

            # 位置描述
            position_desc = {
                'strong': '强势位置 - 接近历史高点',
                'moderate': '中等位置 - 有上涨空间',
                'weak': '弱势位置 - 需要确认'
            }.get(position, '未知位置')

            detail_text = f"""
### {i}. {symbol} {rating_color} {rating}

**基本信息**:
- 突破强度: {strength:.0f}/100
- 当前位置: {position_desc}
- 30天涨幅: {change_30d:+.1f}%

**技术分析**:
"""

            if high_low:
                current_price = high_low.get('current_price', 0)
                max_high = high_low.get('max_high', 0)
                min_low = high_low.get('min_low', 0)
                distance_from_high = high_low.get('distance_from_high_pct', 0)
                rise_from_low = high_low.get('rise_from_low_pct', 0)

                detail_text += f"""- 当前价格: {current_price:.2f} 元
- 近期高点: {max_high:.2f} 元
- 近期低点: {min_low:.2f} 元
- 距离高点: {distance_from_high:.1f}%
- 从低点涨幅: {rise_from_low:.1f}%
"""

            # 突破原因
            if 'breakthrough_reasons' in stock_detail:
                detail_text += f"""
**突破特征**:
"""
                for reason in stock_detail['breakthrough_reasons']:
                    detail_text += f"- {reason}\n"

            # 投资建议
            if strength >= 70:
                suggestion = "建议重点关注，可考虑分批建仓"
            elif strength >= 60:
                suggestion = "建议持续跟踪，等待更强确认信号"
            else:
                suggestion = "建议观望，风险相对较高"

            detail_text += f"""
**投资建议**: {suggestion}

---
"""

            details.append(detail_text)

        return "\n".join(details)

    def _generate_all_stocks_overview(self, results: Dict) -> str:
        """生成全部股票概览"""
        overview = ["| 股票代码 | 突破强度 | 当前位置 | 30天涨幅 | 评级 | 状态 |",
                   "|---------|---------|---------|---------|------|------|"]

        # 先添加有突破的股票
        breakthrough_symbols = {s['symbol'] for s in results['stocks_with_breakthrough']}

        for stock in results['stocks_with_breakthrough']:
            symbol = stock['symbol']
            strength = stock['breakthrough_strength']
            position = stock['current_position']
            change_30d = stock['price_change_30d']

            if strength >= 70:
                rating = "推荐"
                status = "🟢 突破"
            elif strength >= 60:
                rating = "关注"
                status = "🟡 突破"
            else:
                rating = "观望"
                status = "🟡 突破"

            position_cn = {'strong': '强势', 'moderate': '中等', 'weak': '弱势'}.get(position, '未知')

            overview.append(f"| {symbol} | {strength:.0f} | {position_cn} | {change_30d:+.1f}% | {rating} | {status} |")

        # 添加未突破的股票
        for symbol, detail in results['detailed_results'].items():
            if symbol not in breakthrough_symbols:
                change_30d = detail.get('price_change_30d', 0)
                overview.append(f"| {symbol} | - | - | {change_30d:+.1f}% | 观望 | ⚪ 无突破 |")

        return "\n".join(overview)

    def _generate_methodology_section(self) -> str:
        """生成技术分析方法说明"""
        return """
### 分析方法概述

本报告采用**简化突破回调分析法**，通过多维度技术指标识别股票的突破模式。

### 核心指标

1. **突破强度评分** (0-100分)
   - 接近历史高点 (距离<10%): +20-30分
   - 从低点显著上涨 (涨幅>20%): +20-40分
   - 近期表现强势 (30天涨幅>10%): +10-30分

2. **当前位置评估**
   - **强势**: 距离近期高点<5%，处于强势突破状态
   - **中等**: 距离近期高点5-15%，有进一步上涨空间
   - **弱势**: 距离近期高点>15%，需要更多确认

3. **时间周期**
   - 分析周期: 2年历史数据
   - 高低点识别: 最近120个交易日
   - 短期趋势: 最近30个交易日

### 突破模式识别

股票需要同时满足以下条件才被认定为突破模式:
- 突破强度评分 > 50分
- 至少满足2个突破特征
- 30天内有明显价格上涨

### 局限性说明

- 本分析基于技术面，未考虑基本面因素
- 短期市场波动可能影响分析结果
- 建议结合其他分析方法综合判断
"""

    def _generate_risk_disclaimer(self) -> str:
        """生成风险提示和投资建议"""
        return """
### 重要风险提示

⚠️ **本报告仅供参考，不构成投资建议**

**技术分析局限性**:
- 技术分析基于历史价格数据，无法预测未来走势
- 市场情绪、突发事件可能导致技术分析失效
- 短期波动可能与长期趋势不符

**投资风险**:
- 股票投资存在本金损失风险
- 过往表现不代表未来收益
- 市场波动可能超出预期

### 投资建议框架

1. **风险评估**
   - 评估个人风险承受能力
   - 确定合适的投资比例
   - 制定止损策略

2. **多维度分析**
   - 结合基本面分析 (财务状况、行业前景)
   - 关注宏观经济环境
   - 考虑市场情绪和资金流向

3. **投资策略**
   - 分散投资，避免集中持仓
   - 分批建仓，降低时点风险
   - 定期评估，动态调整

4. **持续监控**
   - 跟踪股票基本面变化
   - 关注技术形态演变
   - 及时调整投资策略

**免责声明**: 投资者应根据自身情况谨慎决策，本报告作者不承担任何投资损失责任。
"""

    def _generate_appendix(self, results: Dict) -> str:
        """生成附录信息"""
        appendix = f"""
### 数据来源与处理

**数据来源**: {self.data_config.data_source.upper()}
**数据质量**:
- 成功获取数据: {results['successful_analysis']}/{results['total_stocks']} 只股票
- 数据完整性: 良好
- 数据时效性: 实时更新

### 技术参数设置

- **分析周期**: {results['time_range']}
- **最小数据要求**: 60个交易日
- **高低点识别周期**: 120个交易日
- **短期趋势周期**: 30个交易日

### 评分标准详细说明

**突破强度评分构成**:
- 接近历史高点 (距离<5%): +30分
- 接近历史高点 (距离5-10%): +20分
- 从低点上涨>30%: +40分
- 从低点上涨20-30%: +30分
- 从低点上涨10-20%: +20分
- 30天涨幅>15%: +30分
- 30天涨幅10-15%: +20分
- 30天涨幅5-10%: +10分

**位置评估标准**:
- 强势: 距离高点<5%
- 中等: 距离高点5-15%
- 弱势: 距离高点>15%

### 报告生成信息

- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **分析引擎**: 简化突破回调分析器 v1.0
- **报告版本**: 专业版
"""
        return appendix

def main():
    """主函数"""
    # 测试股票代码
    stock_symbols = [
        '000001',  # 平安银行
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415'   # 海康威视
    ]
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.0
    )
    
    print(f"使用数据源: {data_config.data_source}")
    
    # 创建分析器
    analyzer = SimpleBreakthroughAnalyzer(data_config)
    
    # 执行分析
    results = analyzer.analyze_stocks(stock_symbols, years=2)  # 2年数据，速度更快
    
    # 输出简要结果
    print("\n" + "="*80)
    print("股票突破回调分析结果")
    print("="*80)
    print(f"分析时间: {results['analysis_date']}")
    print(f"成功分析: {results['successful_analysis']}/{results['total_stocks']}")
    print(f"发现突破模式: {len(results['stocks_with_breakthrough'])} 只")

    if results['stocks_with_breakthrough']:
        print("\n🎯 发现突破的股票:")
        for i, stock in enumerate(results['stocks_with_breakthrough'], 1):
            # 评级显示
            strength = stock['breakthrough_strength']
            if strength >= 80:
                rating = "⭐⭐⭐⭐⭐"
            elif strength >= 70:
                rating = "⭐⭐⭐⭐"
            elif strength >= 60:
                rating = "⭐⭐⭐"
            else:
                rating = "⭐⭐"

            print(f"{i}. {stock['symbol']} {rating}")
            print(f"   突破强度: {strength:.0f}/100")
            print(f"   当前位置: {stock['current_position']}")
            print(f"   30天涨幅: {stock['price_change_30d']:+.1f}%")
            print()
    else:
        print("\n❌ 未发现明显的突破模式")

    # 生成详细报告
    print("\n" + "="*80)
    print("正在生成详细分析报告...")
    print("="*80)

    try:
        report_content = analyzer.generate_detailed_report(results)
        print("✅ 详细报告生成成功！")

        # 同时生成JSON格式的数据
        json_file = f"analysis_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"📊 分析数据已保存: {json_file}")

    except Exception as e:
        print(f"❌ 报告生成失败: {e}")

    return results

if __name__ == "__main__":
    main()
