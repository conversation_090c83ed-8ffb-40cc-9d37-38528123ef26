"""
简化版突破回调分析
避免复杂的类型转换问题
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd

from batch_data_loader import BatchDataLoader, BatchDataConfig
from data_models import StockData
from tushare_config import get_tushare_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleBreakthroughAnalyzer:
    """简化版突破回调分析器"""
    
    def __init__(self, data_config: BatchDataConfig):
        self.data_config = data_config
        self.data_loader = BatchDataLoader(data_config)
    
    def analyze_stocks(self, stock_symbols: List[str], years: int = 3) -> Dict:
        """分析股票列表的突破回调模式"""
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y%m%d')
        
        print(f"开始分析 {len(stock_symbols)} 只股票的 {years} 年数据...")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 批量获取数据
        stock_data_dict = self.data_loader.get_batch_stock_data(
            stock_symbols, start_date, end_date
        )
        
        results = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'time_range': f"{start_date} 到 {end_date}",
            'total_stocks': len(stock_symbols),
            'successful_analysis': 0,
            'stocks_with_breakthrough': [],
            'detailed_results': {}
        }
        
        # 分析每只股票
        for symbol in stock_symbols:
            try:
                df = stock_data_dict.get(symbol)
                if df is None or df.empty:
                    logger.warning(f"股票 {symbol} 数据为空，跳过分析")
                    continue
                
                # 转换为StockData格式
                stock_data_list = self._convert_to_stock_data(df, symbol)
                
                # 简化的突破分析
                breakthrough_analysis = self._simple_breakthrough_analysis(stock_data_list)
                
                results['detailed_results'][symbol] = breakthrough_analysis
                results['successful_analysis'] += 1
                
                # 如果发现突破回调模式，加入结果列表
                if breakthrough_analysis['has_breakthrough']:
                    results['stocks_with_breakthrough'].append({
                        'symbol': symbol,
                        'breakthrough_strength': breakthrough_analysis['breakthrough_strength'],
                        'current_position': breakthrough_analysis['current_position'],
                        'price_change_30d': breakthrough_analysis['price_change_30d']
                    })
                
                print(f"✅ {symbol} 分析完成")
                
            except Exception as e:
                logger.error(f"分析股票 {symbol} 失败: {e}")
                continue
        
        # 按突破强度排序
        results['stocks_with_breakthrough'].sort(
            key=lambda x: x['breakthrough_strength'], reverse=True
        )
        
        return results
    
    def _convert_to_stock_data(self, df: pd.DataFrame, symbol: str) -> List[StockData]:
        """将DataFrame转换为StockData列表"""
        stock_data_list = []
        
        for _, row in df.iterrows():
            try:
                # 处理不同数据源的列名
                if 'date' in df.columns:
                    date = pd.to_datetime(row['date'])
                    open_price = float(row['open'])
                    high_price = float(row['high'])
                    low_price = float(row['low'])
                    close_price = float(row['close'])
                    volume = int(row['volume'])
                else:
                    continue
                
                stock_data = StockData(
                    symbol=symbol,
                    date=date,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume
                )
                stock_data_list.append(stock_data)
                
            except Exception as e:
                logger.warning(f"转换数据行失败: {e}")
                continue
        
        return sorted(stock_data_list, key=lambda x: x.date)
    
    def _simple_breakthrough_analysis(self, stock_data_list: List[StockData]) -> Dict:
        """简化的突破分析"""
        analysis = {
            'has_breakthrough': False,
            'breakthrough_strength': 0.0,
            'current_position': 'unknown',
            'price_change_30d': 0.0,
            'high_low_analysis': {}
        }
        
        if len(stock_data_list) < 60:  # 至少需要60天数据
            analysis['reason'] = '数据不足'
            return analysis
        
        # 获取价格数据
        prices = [float(data.close) for data in stock_data_list]
        highs = [float(data.high) for data in stock_data_list]
        lows = [float(data.low) for data in stock_data_list]
        
        current_price = prices[-1]
        
        # 计算30天价格变化
        if len(prices) >= 30:
            price_30d_ago = prices[-30]
            analysis['price_change_30d'] = (current_price - price_30d_ago) / price_30d_ago * 100
        
        # 寻找最近6个月的高低点
        recent_period = min(120, len(prices))  # 最近120天或全部数据
        recent_highs = highs[-recent_period:]
        recent_lows = lows[-recent_period:]
        recent_prices = prices[-recent_period:]
        
        # 找到最高点和最低点
        max_high = max(recent_highs)
        min_low = min(recent_lows)
        max_high_idx = recent_highs.index(max_high)
        min_low_idx = recent_lows.index(min_low)
        
        # 分析突破模式
        # 1. 检查是否接近历史高点
        distance_from_high = (max_high - current_price) / max_high * 100
        
        # 2. 检查是否从低点有显著上涨
        if min_low_idx < len(recent_prices) - 10:  # 低点不是最近10天
            rise_from_low = (current_price - min_low) / min_low * 100
        else:
            rise_from_low = 0
        
        # 3. 判断突破条件
        breakthrough_conditions = []
        
        if distance_from_high < 10:  # 距离高点不超过10%
            breakthrough_conditions.append("接近历史高点")
            
        if rise_from_low > 20:  # 从低点上涨超过20%
            breakthrough_conditions.append(f"从低点上涨{rise_from_low:.1f}%")
            
        if analysis['price_change_30d'] > 10:  # 30天涨幅超过10%
            breakthrough_conditions.append(f"30天涨幅{analysis['price_change_30d']:.1f}%")
        
        # 计算突破强度
        breakthrough_strength = 0
        if distance_from_high < 5:
            breakthrough_strength += 30
        elif distance_from_high < 10:
            breakthrough_strength += 20
            
        if rise_from_low > 30:
            breakthrough_strength += 40
        elif rise_from_low > 20:
            breakthrough_strength += 30
        elif rise_from_low > 10:
            breakthrough_strength += 20
            
        if analysis['price_change_30d'] > 15:
            breakthrough_strength += 30
        elif analysis['price_change_30d'] > 10:
            breakthrough_strength += 20
        elif analysis['price_change_30d'] > 5:
            breakthrough_strength += 10
        
        # 判断当前位置
        if distance_from_high < 5:
            position = 'strong'
        elif distance_from_high < 15:
            position = 'moderate'
        else:
            position = 'weak'
        
        # 更新分析结果
        if breakthrough_strength > 50 and len(breakthrough_conditions) >= 2:
            analysis['has_breakthrough'] = True
            analysis['breakthrough_strength'] = breakthrough_strength
            analysis['current_position'] = position
            analysis['breakthrough_reasons'] = breakthrough_conditions
        
        # 高低点分析
        analysis['high_low_analysis'] = {
            'max_high': max_high,
            'min_low': min_low,
            'current_price': current_price,
            'distance_from_high_pct': distance_from_high,
            'rise_from_low_pct': rise_from_low
        }
        
        return analysis

def main():
    """主函数"""
    # 测试股票代码
    stock_symbols = [
        '000001',  # 平安银行
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415'   # 海康威视
    ]
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.0
    )
    
    print(f"使用数据源: {data_config.data_source}")
    
    # 创建分析器
    analyzer = SimpleBreakthroughAnalyzer(data_config)
    
    # 执行分析
    results = analyzer.analyze_stocks(stock_symbols, years=2)  # 2年数据，速度更快
    
    # 输出结果
    print("\n" + "="*80)
    print("简化版突破分析结果")
    print("="*80)
    print(f"分析时间: {results['analysis_date']}")
    print(f"成功分析: {results['successful_analysis']}/{results['total_stocks']}")
    print(f"发现突破模式: {len(results['stocks_with_breakthrough'])} 只")
    
    if results['stocks_with_breakthrough']:
        print("\n🎯 发现突破的股票:")
        for i, stock in enumerate(results['stocks_with_breakthrough'], 1):
            print(f"{i}. {stock['symbol']}")
            print(f"   突破强度: {stock['breakthrough_strength']:.0f}")
            print(f"   当前位置: {stock['current_position']}")
            print(f"   30天涨幅: {stock['price_change_30d']:.1f}%")
            print()
    else:
        print("\n❌ 未发现明显的突破模式")
    
    return results

if __name__ == "__main__":
    main()
