import tushare as ts
import pandas as pd
from typing import List, Optional
from decimal import Decimal
from datetime import datetime
import logging
import time
from data_models import StockData

logger = logging.getLogger(__name__)

class TushareDataLoader:
    """Tushare数据加载器"""

    def __init__(self, token: str = None):
        """
        初始化Tushare数据加载器

        Args:
            token: Tushare API token，如果为None则从环境变量获取
        """
        if token:
            ts.set_token(token)

        try:
            self.pro = ts.pro_api()
            logger.info("Tushare API初始化成功")
        except Exception as e:
            logger.error(f"Tushare API初始化失败: {e}")
            raise RuntimeError("请设置有效的Tushare token")

    def _convert_stock_code(self, stock_code: str) -> str:
        """
        转换股票代码格式为Tushare格式

        Args:
            stock_code: 6位股票代码 (如 "000001")

        Returns:
            Tushare格式代码 (如 "000001.SZ")
        """
        if '.' in stock_code:
            return stock_code  # 已经是正确格式

        # 根据代码前缀判断交易所
        if stock_code.startswith(('000', '002', '300')):
            return f"{stock_code}.SZ"  # 深交所
        elif stock_code.startswith(('600', '601', '603', '605', '688')):
            return f"{stock_code}.SH"  # 上交所
        else:
            # 默认深交所
            return f"{stock_code}.SZ"

    def _convert_date_format(self, date_str: str) -> str:
        """
        转换日期格式为Tushare格式

        Args:
            date_str: YYYYMMDD格式日期

        Returns:
            YYYY-MM-DD格式日期
        """
        if len(date_str) == 8:
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return date_str

def get_stock_data(stock_code: str, start_date: str, end_date: str,
                  token: str = None, adj: str = 'qfq') -> pd.DataFrame:
    """
    从 Tushare 获取股票历史数据

    Args:
        stock_code: 股票代码 (如 "600000" 或 "600000.SH")
        start_date: 开始日期 (格式 "YYYYMMDD" 或 "YYYY-MM-DD")
        end_date: 结束日期 (格式 "YYYYMMDD" 或 "YYYY-MM-DD")
        token: Tushare API token
        adj: 复权类型 ('qfq'-前复权, 'hfq'-后复权, None-不复权)

    Returns:
        包含 OHLCV 数据的 DataFrame
    """
    try:
        loader = TushareDataLoader(token)

        # 转换股票代码和日期格式
        ts_code = loader._convert_stock_code(stock_code)
        start_date_formatted = loader._convert_date_format(start_date)
        end_date_formatted = loader._convert_date_format(end_date)

        logger.info(f"获取 {ts_code} 数据: {start_date_formatted} 到 {end_date_formatted}")

        # 获取日线数据
        df = loader.pro.daily(
            ts_code=ts_code,
            start_date=start_date_formatted.replace('-', ''),
            end_date=end_date_formatted.replace('-', ''),
            adj=adj
        )

        if df.empty:
            logger.warning(f"股票 {stock_code} 无数据")
            return pd.DataFrame()

        # 规范列名
        df = df.rename(columns={
            'trade_date': 'date',
            'ts_code': 'symbol',
            'vol': 'volume',
            'amount': 'turnover'  # 成交额
        })

        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)

        # 计算平均成交量（20日移动平均）
        df['avg_vol'] = df['volume'].rolling(window=20, min_periods=1).mean()

        # 计算换手率（如果有流通股本数据）
        try:
            # 获取基本信息
            basic_info = loader.pro.stock_basic(ts_code=ts_code, fields='ts_code,float_share')
            if not basic_info.empty:
                float_share = basic_info.iloc[0]['float_share'] * 10000  # 万股转为股
                df['turnover_rate'] = (df['volume'] / float_share * 100).round(4)
            else:
                df['turnover_rate'] = None
        except:
            df['turnover_rate'] = None

        logger.info(f"成功获取 {stock_code} 数据: {len(df)} 条记录")

        return df[['date', 'open', 'high', 'low', 'close', 'volume', 'avg_vol', 'turnover_rate']]

    except Exception as e:
        logger.error(f"获取 {stock_code} 数据失败: {e}")
        raise RuntimeError(f"获取 {stock_code} 数据失败: {str(e)}")

def get_stock_basic_info(token: str = None) -> pd.DataFrame:
    """
    获取股票基本信息

    Args:
        token: Tushare API token

    Returns:
        股票基本信息DataFrame
    """
    try:
        loader = TushareDataLoader(token)

        # 获取股票基本信息
        df = loader.pro.stock_basic(
            exchange='',
            list_status='L',  # 上市状态
            fields='ts_code,symbol,name,area,industry,market,list_date'
        )

        logger.info(f"获取股票基本信息: {len(df)} 只股票")
        return df

    except Exception as e:
        logger.error(f"获取股票基本信息失败: {e}")
        raise RuntimeError(f"获取股票基本信息失败: {str(e)}")

def get_stock_realtime_data(symbols: List[str] = None, token: str = None) -> pd.DataFrame:
    """
    获取股票实时行情数据

    Args:
        symbols: 股票代码列表
        token: Tushare API token

    Returns:
        实时行情DataFrame
    """
    try:
        loader = TushareDataLoader(token)

        # 如果没有指定股票，获取所有股票的实时数据
        if symbols:
            ts_codes = [loader._convert_stock_code(code) for code in symbols]
            ts_codes_str = ','.join(ts_codes)
        else:
            ts_codes_str = ''

        # 获取实时行情
        df = loader.pro.daily_basic(
            ts_code=ts_codes_str,
            trade_date='',  # 最新交易日
            fields='ts_code,trade_date,close,turnover_rate,volume_ratio,pe,pb,ps,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
        )

        logger.info(f"获取实时行情数据: {len(df)} 条记录")
        return df

    except Exception as e:
        logger.error(f"获取实时行情失败: {e}")
        raise RuntimeError(f"获取实时行情失败: {str(e)}")

def dataframe_to_stock_data(df: pd.DataFrame, symbol: str) -> List[StockData]:
    """将DataFrame转换为StockData列表"""
    stock_data_list = []
    
    for _, row in df.iterrows():
        stock_data = StockData(
            symbol=symbol,
            date=row['date'].to_pydatetime(),
            open=Decimal(str(row['open'])),
            high=Decimal(str(row['high'])),
            low=Decimal(str(row['low'])),
            close=Decimal(str(row['close'])),
            volume=int(row['volume'])
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list