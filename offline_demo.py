"""
离线演示脚本
使用模拟数据演示系统功能，无需网络连接
"""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict
import random

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入数据模型
from data_models import StockData
from zigzag import ZigZagState, update_state, BatchZigZagProcessor

def create_mock_stock_pool():
    """创建模拟股票池"""
    print("\n" + "="*60)
    print("演示1: 模拟股票池")
    print("="*60)
    
    # 模拟股票池数据
    mock_stocks = [
        {'symbol': '000001', 'name': '平安银行', 'market_cap': 2500.0, 'price': 12.50},
        {'symbol': '000002', 'name': '万科A', 'market_cap': 1800.0, 'price': 16.20},
        {'symbol': '600000', 'name': '浦发银行', 'market_cap': 2200.0, 'price': 8.90},
        {'symbol': '600036', 'name': '招商银行', 'market_cap': 8500.0, 'price': 42.30},
        {'symbol': '000858', 'name': '五粮液', 'market_cap': 6800.0, 'price': 175.60},
        {'symbol': '600519', 'name': '贵州茅台', 'market_cap': 22000.0, 'price': 1750.00},
        {'symbol': '000063', 'name': '中兴通讯', 'market_cap': 1200.0, 'price': 25.80},
        {'symbol': '002415', 'name': '海康威视', 'market_cap': 3200.0, 'price': 34.50},
        {'symbol': '300059', 'name': '东方财富', 'market_cap': 2800.0, 'price': 18.20},
        {'symbol': '002594', 'name': '比亚迪', 'market_cap': 7500.0, 'price': 260.00}
    ]
    
    print(f"模拟股票池包含 {len(mock_stocks)} 只股票:")
    for i, stock in enumerate(mock_stocks):
        print(f"  {i+1:2d}. {stock['symbol']} {stock['name']} "
              f"市值:{stock['market_cap']:.0f}亿 价格:{stock['price']:.2f}")
    
    return mock_stocks

def create_mock_stock_data(symbol: str, days: int = 100) -> List[StockData]:
    """创建模拟股票数据"""
    random.seed(hash(symbol) % 1000)  # 基于股票代码的固定种子
    
    base_date = datetime.now() - timedelta(days=days)
    stock_data_list = []
    
    # 根据股票设置不同的基础价格
    price_map = {
        '000001': 12.50, '000002': 16.20, '600000': 8.90, '600036': 42.30,
        '000858': 175.60, '600519': 1750.00, '000063': 25.80, '002415': 34.50,
        '300059': 18.20, '002594': 260.00
    }
    base_price = price_map.get(symbol, 30.0)
    current_price = base_price
    
    for day in range(days):
        # 添加趋势和噪音
        if day < days * 0.3:
            trend = 0.008  # 上升趋势
        elif day < days * 0.7:
            trend = -0.006  # 下降趋势
        else:
            trend = 0.005  # 再次上升
        
        # 添加随机噪音
        noise = random.uniform(-0.03, 0.03)
        daily_change = trend + noise
        
        # 计算OHLC
        open_price = current_price
        close_price = current_price * (1 + daily_change)
        
        # 日内波动
        volatility = 0.015
        high_price = max(open_price, close_price) * (1 + random.uniform(0, volatility))
        low_price = min(open_price, close_price) * (1 - random.uniform(0, volatility))
        
        date = base_date + timedelta(days=day)
        stock_data = StockData(
            symbol=symbol,
            date=date,
            open=Decimal(f"{open_price:.2f}"),
            high=Decimal(f"{high_price:.2f}"),
            low=Decimal(f"{low_price:.2f}"),
            close=Decimal(f"{close_price:.2f}"),
            volume=random.randint(800000, 1200000)
        )
        stock_data_list.append(stock_data)
        current_price = close_price
    
    return stock_data_list

def demo_batch_zigzag_analysis():
    """演示批量ZigZag分析"""
    print("\n" + "="*60)
    print("演示2: 批量ZigZag分析")
    print("="*60)
    
    # 创建模拟股票池
    mock_stocks = create_mock_stock_pool()
    
    # 为每只股票创建模拟数据
    stock_data_dict = {}
    print(f"\n生成模拟历史数据:")
    
    for stock in mock_stocks[:5]:  # 只处理前5只股票进行演示
        symbol = stock['symbol']
        stock_data_list = create_mock_stock_data(symbol, days=90)
        stock_data_dict[symbol] = stock_data_list
        print(f"  {symbol}: {len(stock_data_list)} 条记录")
    
    # 批量ZigZag分析
    print(f"\n开始批量ZigZag分析:")
    
    zigzag_config = {
        'atr_period': 14,
        'atr_multiplier': 1.5,
        'min_price_move': 0,
        'min_trend_bars': 1
    }
    
    processor = BatchZigZagProcessor(zigzag_config)
    zigzag_states = processor.process_stock_pool_batch(stock_data_dict)
    
    print(f"\n分析结果:")
    for symbol, state in zigzag_states.items():
        points_count = len(state.confirmed_points)
        trend = state.trend or "无趋势"
        print(f"  {symbol}: {points_count}个拐点, 当前趋势: {trend}, 趋势持续: {state.bars_in_trend}天")
        
        # 显示最近的拐点
        if state.confirmed_points:
            recent_points = state.confirmed_points[-3:]  # 最近3个拐点
            print(f"    最近拐点:")
            for point in recent_points:
                print(f"      {point.date} {point.point_type} {point.price}")
    
    # 显示整体摘要
    summary = processor.get_batch_summary(zigzag_states)
    print(f"\n整体摘要:")
    print(f"  总股票数: {summary['total_stocks']}")
    print(f"  有拐点的股票: {summary['stocks_with_points']}")
    print(f"  总拐点数: {summary['total_confirmed_points']}")
    print(f"  平均每股拐点数: {summary['avg_points_per_stock']:.1f}")
    
    # 趋势分布
    print(f"  当前趋势分布:")
    for trend, count in summary['trend_distribution'].items():
        percentage = count / summary['total_stocks'] * 100 if summary['total_stocks'] > 0 else 0
        print(f"    {trend}: {count}只 ({percentage:.1f}%)")
    
    return stock_data_dict, zigzag_states

def demo_strategy_signals(stock_data_dict, zigzag_states):
    """演示策略信号生成"""
    print("\n" + "="*60)
    print("演示3: 策略信号生成")
    print("="*60)
    
    if not stock_data_dict or not zigzag_states:
        print("没有足够的数据，跳过演示")
        return
    
    try:
        from strategy_interface import ZigZagTrendStrategy, Signal
        
        # 创建策略实例
        strategy = ZigZagTrendStrategy(min_trend_strength=3, min_price_change=0.05)
        
        print("策略配置:")
        info = strategy.get_strategy_info()
        print(f"  策略名称: {info['name']}")
        print(f"  策略类型: {info['type']}")
        print(f"  最小趋势强度: {strategy.min_trend_strength}")
        print(f"  最小价格变动: {strategy.min_price_change:.1%}")
        
        # 为每只股票生成信号
        total_signals = 0
        print(f"\n策略信号生成结果:")
        
        for symbol in stock_data_dict.keys():
            if symbol in zigzag_states:
                stock_data = stock_data_dict[symbol]
                zigzag_state = zigzag_states[symbol]
                
                signals = strategy.analyze(stock_data, zigzag_state)
                
                if signals:
                    print(f"  {symbol}: {len(signals)} 个信号")
                    for signal in signals:
                        print(f"    {signal.date.strftime('%Y-%m-%d')} {signal.signal_type} "
                              f"价格:{signal.price:.2f} 强度:{signal.strength:.1f} "
                              f"置信度:{signal.confidence:.1f}")
                        print(f"    原因: {signal.reason}")
                    total_signals += len(signals)
                else:
                    print(f"  {symbol}: 无信号")
        
        print(f"\n总共生成 {total_signals} 个策略信号")
        
    except Exception as e:
        print(f"策略信号演示失败: {e}")
        logger.error(f"策略演示失败: {e}")

def demo_data_analysis(stock_data_dict, zigzag_states):
    """演示数据分析功能"""
    print("\n" + "="*60)
    print("演示4: 数据分析统计")
    print("="*60)
    
    if not stock_data_dict:
        print("没有数据，跳过演示")
        return
    
    # 价格统计
    print("价格统计分析:")
    for symbol, stock_data_list in stock_data_dict.items():
        if stock_data_list:
            prices = [float(data.close) for data in stock_data_list]
            volumes = [data.volume for data in stock_data_list]
            
            price_change = (prices[-1] - prices[0]) / prices[0] * 100
            avg_volume = sum(volumes) / len(volumes)
            
            print(f"  {symbol}:")
            print(f"    期间涨跌幅: {price_change:+.2f}%")
            print(f"    价格区间: {min(prices):.2f} - {max(prices):.2f}")
            print(f"    平均成交量: {avg_volume:,.0f}")
    
    # ZigZag统计
    print(f"\nZigZag分析统计:")
    for symbol, state in zigzag_states.items():
        if state.confirmed_points:
            high_points = [p for p in state.confirmed_points if p.point_type == 'HIGH']
            low_points = [p for p in state.confirmed_points if p.point_type == 'LOW']
            
            print(f"  {symbol}:")
            print(f"    总拐点: {len(state.confirmed_points)} (高点:{len(high_points)}, 低点:{len(low_points)})")
            print(f"    当前趋势: {state.trend or '无'} (持续{state.bars_in_trend}天)")
            
            if len(state.confirmed_points) >= 2:
                # 计算最近一段趋势的幅度
                recent_points = state.confirmed_points[-2:]
                price_move = abs(float(recent_points[1].price - recent_points[0].price))
                move_pct = price_move / float(recent_points[0].price) * 100
                print(f"    最近趋势幅度: {move_pct:.2f}%")

def main():
    """主演示函数"""
    print("股票分析系统离线功能演示")
    print("本演示使用模拟数据展示系统的主要功能")
    
    try:
        # 1. 创建模拟股票池
        mock_stocks = create_mock_stock_pool()
        
        # 2. 批量ZigZag分析
        stock_data_dict, zigzag_states = demo_batch_zigzag_analysis()
        
        # 3. 策略信号生成
        demo_strategy_signals(stock_data_dict, zigzag_states)
        
        # 4. 数据分析统计
        demo_data_analysis(stock_data_dict, zigzag_states)
        
        print("\n" + "="*60)
        print("离线演示完成！")
        print("="*60)
        print("系统功能演示已全部完成。")
        print("注意: 这是使用模拟数据的离线演示。")
        print("要使用真实数据，请确保网络连接正常并安装所有依赖。")
        print("\n系统特点:")
        print("✅ 模块化设计 - 各功能模块独立")
        print("✅ 批量处理 - 支持多只股票并行分析") 
        print("✅ 智能算法 - 基于ATR动态阈值的ZigZag")
        print("✅ 数据库存储 - DuckDB高性能存储")
        print("✅ 策略框架 - 可扩展的策略开发接口")
        print("✅ 可视化支持 - 专业K线图表")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        logger.error(f"演示失败: {e}")

if __name__ == "__main__":
    main()
