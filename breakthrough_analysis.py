"""
突破回调分析模块
分析股票的ZigZag峰谷数据，识别突破和回调模式
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd

from batch_data_loader import BatchDataLoader, BatchDataConfig
from zigzag_analysis import ZigZagCalculator
from zigzag import ZigZagState
from data_models import StockData
from tushare_config import get_tushare_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BreakthroughAnalyzer:
    """突破回调分析器"""
    
    def __init__(self, data_config: BatchDataConfig):
        self.data_config = data_config
        self.data_loader = BatchDataLoader(data_config)
        self.zigzag_calculator = ZigZagCalculator(atr_period=14, atr_multiplier=2.0)
    
    def analyze_stocks(self, stock_symbols: List[str], years: int = 5) -> Dict:
        """
        分析股票列表的突破回调模式
        
        Args:
            stock_symbols: 股票代码列表
            years: 分析年数
            
        Returns:
            分析结果字典
        """
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y%m%d')
        
        print(f"开始分析 {len(stock_symbols)} 只股票的 {years} 年数据...")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 批量获取数据
        stock_data_dict = self.data_loader.get_batch_stock_data(
            stock_symbols, start_date, end_date
        )
        
        results = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'time_range': f"{start_date} 到 {end_date}",
            'total_stocks': len(stock_symbols),
            'successful_analysis': 0,
            'stocks_with_breakthrough': [],
            'detailed_results': {}
        }
        
        # 分析每只股票
        for symbol in stock_symbols:
            try:
                df = stock_data_dict.get(symbol)
                if df is None or df.empty:
                    logger.warning(f"股票 {symbol} 数据为空，跳过分析")
                    continue
                
                # 转换为StockData格式
                stock_data_list = self._convert_to_stock_data(df, symbol)
                
                # 进行ZigZag分析
                zigzag_state = self._analyze_zigzag(stock_data_list)
                
                # 分析突破回调模式
                breakthrough_analysis = self._analyze_breakthrough_pullback(
                    stock_data_list, zigzag_state
                )
                
                results['detailed_results'][symbol] = breakthrough_analysis
                results['successful_analysis'] += 1
                
                # 如果发现突破回调模式，加入结果列表
                if breakthrough_analysis['has_breakthrough_pullback']:
                    results['stocks_with_breakthrough'].append({
                        'symbol': symbol,
                        'breakthrough_strength': breakthrough_analysis['breakthrough_strength'],
                        'pullback_depth': breakthrough_analysis['pullback_depth'],
                        'current_position': breakthrough_analysis['current_position']
                    })
                
                print(f"✅ {symbol} 分析完成")
                
            except Exception as e:
                logger.error(f"分析股票 {symbol} 失败: {e}")
                continue
        
        # 按突破强度排序
        results['stocks_with_breakthrough'].sort(
            key=lambda x: x['breakthrough_strength'], reverse=True
        )
        
        return results
    
    def _convert_to_stock_data(self, df: pd.DataFrame, symbol: str) -> List[StockData]:
        """将DataFrame转换为StockData列表"""
        stock_data_list = []
        
        for _, row in df.iterrows():
            try:
                # 处理不同数据源的列名
                if 'trade_date' in df.columns:  # Tushare格式
                    date = pd.to_datetime(str(row['trade_date']), format='%Y%m%d')
                    open_price = row['open']
                    high_price = row['high']
                    low_price = row['low']
                    close_price = row['close']
                    volume = row['vol']
                else:  # AKShare格式
                    date = pd.to_datetime(row['日期'])
                    open_price = row['开盘']
                    high_price = row['最高']
                    low_price = row['最低']
                    close_price = row['收盘']
                    volume = row['成交量']
                
                stock_data = StockData(
                    symbol=symbol,
                    date=date,
                    open=float(open_price),
                    high=float(high_price),
                    low=float(low_price),
                    close=float(close_price),
                    volume=int(volume)
                )
                stock_data_list.append(stock_data)
                
            except Exception as e:
                logger.warning(f"转换数据行失败: {e}")
                continue
        
        return sorted(stock_data_list, key=lambda x: x.date)
    
    def _analyze_zigzag(self, stock_data_list: List[StockData]) -> ZigZagState:
        """进行ZigZag分析"""
        zigzag_state = ZigZagState()
        
        for data in stock_data_list:
            self.zigzag_calculator.update(data, zigzag_state)
        
        return zigzag_state
    
    def _analyze_breakthrough_pullback(self, stock_data_list: List[StockData], 
                                     zigzag_state: ZigZagState) -> Dict:
        """
        分析突破回调模式
        
        突破回调模式定义：
        1. 有明显的峰谷结构
        2. 最近有突破前期高点
        3. 突破后有回调但未跌破关键支撑
        4. 当前价格处于相对强势位置
        """
        analysis = {
            'has_breakthrough_pullback': False,
            'breakthrough_strength': 0.0,
            'pullback_depth': 0.0,
            'current_position': 'unknown',
            'peak_valley_analysis': {},
            'recent_patterns': []
        }
        
        if len(zigzag_state.confirmed_points) < 4:
            analysis['reason'] = '确认的ZigZag点数量不足'
            return analysis
        
        # 获取最近的峰谷点
        recent_points = zigzag_state.confirmed_points[-6:]  # 最近6个点
        current_price = stock_data_list[-1].close
        
        # 分析峰谷幅度
        peak_valley_stats = self._calculate_peak_valley_stats(zigzag_state.confirmed_points)
        analysis['peak_valley_analysis'] = peak_valley_stats
        
        # 寻找突破模式
        breakthrough_info = self._find_breakthrough_pattern(recent_points, current_price)
        
        if breakthrough_info['found']:
            analysis['has_breakthrough_pullback'] = True
            analysis['breakthrough_strength'] = breakthrough_info['strength']
            analysis['pullback_depth'] = breakthrough_info['pullback_depth']
            analysis['current_position'] = breakthrough_info['position']
            analysis['recent_patterns'] = breakthrough_info['patterns']
        
        return analysis
    
    def _calculate_peak_valley_stats(self, confirmed_points) -> Dict:
        """计算峰谷统计数据"""
        if len(confirmed_points) < 2:
            return {}
        
        peaks = [p for p in confirmed_points if p.point_type == 'HIGH']
        valleys = [p for p in confirmed_points if p.point_type == 'LOW']
        
        stats = {
            'total_peaks': len(peaks),
            'total_valleys': len(valleys),
            'avg_peak_valley_amplitude': 0.0,
            'max_amplitude': 0.0,
            'recent_amplitude_trend': 'stable'
        }
        
        # 计算峰谷幅度
        amplitudes = []
        for i in range(len(confirmed_points) - 1):
            current = confirmed_points[i]
            next_point = confirmed_points[i + 1]
            amplitude = abs(next_point.price - current.price) / current.price * 100
            amplitudes.append(amplitude)
        
        if amplitudes:
            stats['avg_peak_valley_amplitude'] = sum(amplitudes) / len(amplitudes)
            stats['max_amplitude'] = max(amplitudes)
            
            # 分析最近的幅度趋势
            if len(amplitudes) >= 4:
                recent_avg = sum(amplitudes[-2:]) / 2
                earlier_avg = sum(amplitudes[-4:-2]) / 2
                if recent_avg > earlier_avg * 1.2:
                    stats['recent_amplitude_trend'] = 'increasing'
                elif recent_avg < earlier_avg * 0.8:
                    stats['recent_amplitude_trend'] = 'decreasing'
        
        return stats
    
    def _find_breakthrough_pattern(self, recent_points, current_price) -> Dict:
        """寻找突破回调模式"""
        result = {
            'found': False,
            'strength': 0.0,
            'pullback_depth': 0.0,
            'position': 'unknown',
            'patterns': []
        }
        
        if len(recent_points) < 4:
            return result
        
        # 寻找最近的高点突破
        peaks = [p for p in recent_points if p.point_type == 'HIGH']
        valleys = [p for p in recent_points if p.point_type == 'LOW']
        
        if len(peaks) < 2 or len(valleys) < 2:
            return result
        
        # 检查是否有突破前期高点
        latest_peak = max(peaks, key=lambda x: x.date)
        earlier_peaks = [p for p in peaks if p.date < latest_peak.date]
        
        if not earlier_peaks:
            return result
        
        highest_earlier_peak = max(earlier_peaks, key=lambda x: x.price)
        
        # 判断是否突破
        if latest_peak.price > highest_earlier_peak.price:
            breakthrough_strength = (latest_peak.price - highest_earlier_peak.price) / highest_earlier_peak.price * 100
            
            # 检查突破后的回调
            latest_valley_after_breakthrough = None
            for v in valleys:
                if v.date > latest_peak.date:
                    latest_valley_after_breakthrough = v
                    break
            
            if latest_valley_after_breakthrough:
                pullback_depth = (latest_peak.price - latest_valley_after_breakthrough.price) / latest_peak.price * 100
                
                # 判断回调是否合理（不超过50%）
                if pullback_depth < 50:
                    result['found'] = True
                    result['strength'] = breakthrough_strength
                    result['pullback_depth'] = pullback_depth
                    
                    # 判断当前位置
                    if current_price > latest_peak.price * 0.9:
                        result['position'] = 'strong'
                    elif current_price > latest_valley_after_breakthrough.price * 1.1:
                        result['position'] = 'moderate'
                    else:
                        result['position'] = 'weak'
                    
                    result['patterns'].append(f"突破前高 {breakthrough_strength:.1f}%")
                    result['patterns'].append(f"回调幅度 {pullback_depth:.1f}%")
        
        return result

def main():
    """主函数 - 演示突破回调分析"""
    # 示例股票代码（你可以修改这个列表）
    stock_symbols = [
        '000001',  # 平安银行
        '000002',  # 万科A
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415',  # 海康威视
        '600276',  # 恒瑞医药
        '000725',  # 京东方A
        '002594',  # 比亚迪
        '600887'   # 伊利股份
    ]
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=2,
        request_delay=0.8  # 保守的请求间隔
    )
    
    print(f"使用数据源: {data_config.data_source}")
    
    # 创建分析器
    analyzer = BreakthroughAnalyzer(data_config)
    
    # 执行分析
    results = analyzer.analyze_stocks(stock_symbols, years=5)
    
    # 输出结果
    print("\n" + "="*80)
    print("突破回调分析结果")
    print("="*80)
    print(f"分析时间: {results['analysis_date']}")
    print(f"数据范围: {results['time_range']}")
    print(f"总股票数: {results['total_stocks']}")
    print(f"成功分析: {results['successful_analysis']}")
    print(f"发现突破回调模式: {len(results['stocks_with_breakthrough'])} 只")
    
    if results['stocks_with_breakthrough']:
        print("\n🎯 发现突破回调的股票:")
        for i, stock in enumerate(results['stocks_with_breakthrough'][:5], 1):
            print(f"{i}. {stock['symbol']}")
            print(f"   突破强度: {stock['breakthrough_strength']:.1f}%")
            print(f"   回调深度: {stock['pullback_depth']:.1f}%")
            print(f"   当前位置: {stock['current_position']}")
            print()
    else:
        print("\n❌ 未发现明显的突破回调模式")
    
    return results

if __name__ == "__main__":
    main()
