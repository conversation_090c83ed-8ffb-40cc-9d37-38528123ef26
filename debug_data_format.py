"""
数据格式调试工具
用于检查数据源返回的数据格式
"""

from batch_data_loader import BatchDataLoader, BatchDataConfig
from tushare_config import get_tushare_config
from datetime import datetime, timedelta

def debug_data_format():
    """调试数据格式"""
    print("🔍 数据格式调试工具")
    print("="*50)
    
    # 配置数据源
    tushare_config = get_tushare_config()
    
    # 测试Tushare
    if tushare_config.is_configured():
        print("\n📊 测试Tushare数据格式:")
        data_config = BatchDataConfig(
            tushare_token=tushare_config.get_token(),
            data_source='tushare',
            max_workers=1,
            request_delay=1.0
        )
        
        loader = BatchDataLoader(data_config)
        
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        try:
            df = loader.get_single_stock_data('000001', start_date, end_date)
            if df is not None and not df.empty:
                print(f"✅ Tushare数据获取成功")
                print(f"数据形状: {df.shape}")
                print(f"列名: {list(df.columns)}")
                print(f"前3行数据:")
                print(df.head(3))
                print(f"数据类型:")
                print(df.dtypes)
            else:
                print("❌ Tushare数据获取失败")
        except Exception as e:
            print(f"❌ Tushare测试失败: {e}")
    
    # 测试AKShare
    print("\n📊 测试AKShare数据格式:")
    data_config = BatchDataConfig(
        data_source='akshare',
        max_workers=1,
        request_delay=1.0
    )
    
    loader = BatchDataLoader(data_config)
    
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    try:
        df = loader.get_single_stock_data('000001', start_date, end_date)
        if df is not None and not df.empty:
            print(f"✅ AKShare数据获取成功")
            print(f"数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            print(f"前3行数据:")
            print(df.head(3))
            print(f"数据类型:")
            print(df.dtypes)
        else:
            print("❌ AKShare数据获取失败")
    except Exception as e:
        print(f"❌ AKShare测试失败: {e}")

def test_data_conversion():
    """测试数据转换"""
    print("\n🔄 测试数据转换功能:")
    
    from breakthrough_analysis import BreakthroughAnalyzer
    
    # 配置
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.0
    )
    
    print(f"使用数据源: {data_config.data_source}")
    
    # 创建分析器
    analyzer = BreakthroughAnalyzer(data_config)
    
    # 获取测试数据
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    try:
        df = analyzer.data_loader.get_single_stock_data('000001', start_date, end_date)
        if df is not None and not df.empty:
            print(f"✅ 原始数据获取成功: {df.shape}")
            
            # 测试转换
            stock_data_list = analyzer._convert_to_stock_data(df, '000001')
            print(f"✅ 数据转换成功: {len(stock_data_list)} 条记录")
            
            if stock_data_list:
                sample = stock_data_list[0]
                print(f"样本数据: {sample.date} 开:{sample.open} 高:{sample.high} 低:{sample.low} 收:{sample.close}")
            
        else:
            print("❌ 数据获取失败")
            
    except Exception as e:
        print(f"❌ 转换测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_format()
    test_data_conversion()
