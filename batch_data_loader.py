"""
批量股票数据获取模块
支持高效批量获取多只股票的历史数据
"""

import pandas as pd
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import logging
from dataclasses import dataclass
from decimal import Decimal
import json
import os

from data_models import StockData
from stock_pool import StockInfo
from data_loader import get_stock_data, TushareDataLoader

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class BatchDataConfig:
    """批量数据获取配置"""
    max_workers: int = 3  # 最大并发线程数（tushare建议较少并发）
    request_delay: float = 0.2  # 请求间隔（秒）
    retry_count: int = 3  # 重试次数
    cache_enabled: bool = True  # 是否启用缓存
    cache_dir: str = "data_cache"  # 缓存目录
    tushare_token: str = None  # Tushare API token
    data_source: str = "tushare"  # 数据源 ("tushare" 或 "akshare")
    
class BatchDataLoader:
    """批量股票数据加载器"""
    
    def __init__(self, config: BatchDataConfig = None):
        self.config = config or BatchDataConfig()
        
        # 创建缓存目录
        if self.config.cache_enabled:
            os.makedirs(self.config.cache_dir, exist_ok=True)
    
    def _get_cache_filename(self, symbol: str, start_date: str, end_date: str) -> str:
        """生成缓存文件名"""
        return os.path.join(
            self.config.cache_dir, 
            f"{symbol}_{start_date}_{end_date}.json"
        )
    
    def _load_from_cache(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从缓存加载数据"""
        if not self.config.cache_enabled:
            return None
            
        cache_file = self._get_cache_filename(symbol, start_date, end_date)
        
        try:
            if os.path.exists(cache_file):
                # 检查缓存文件是否过期（1天）
                file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
                if datetime.now() - file_time < timedelta(days=1):
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    df = pd.DataFrame(data)
                    df['date'] = pd.to_datetime(df['date'])
                    logger.debug(f"从缓存加载 {symbol} 数据")
                    return df
        except Exception as e:
            logger.warning(f"加载缓存失败 {symbol}: {e}")
        
        return None
    
    def _save_to_cache(self, df: pd.DataFrame, symbol: str, start_date: str, end_date: str):
        """保存数据到缓存"""
        if not self.config.cache_enabled:
            return
            
        cache_file = self._get_cache_filename(symbol, start_date, end_date)
        
        try:
            # 转换为可序列化的格式
            cache_data = df.copy()
            cache_data['date'] = cache_data['date'].dt.strftime('%Y-%m-%d')
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data.to_dict('records'), f, ensure_ascii=False, indent=2)
            
            logger.debug(f"缓存 {symbol} 数据到 {cache_file}")
            
        except Exception as e:
            logger.warning(f"保存缓存失败 {symbol}: {e}")
    
    def get_single_stock_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取单只股票数据（带缓存和重试）"""

        # 尝试从缓存加载
        cached_data = self._load_from_cache(symbol, start_date, end_date)
        if cached_data is not None:
            return cached_data

        # 从API获取数据
        for attempt in range(self.config.retry_count):
            try:
                logger.debug(f"获取 {symbol} 数据 (尝试 {attempt + 1}/{self.config.retry_count})")

                if self.config.data_source == "tushare":
                    # 使用Tushare获取数据
                    df = get_stock_data(
                        stock_code=symbol,
                        start_date=start_date,
                        end_date=end_date,
                        token=self.config.tushare_token,
                        adj='qfq'
                    )
                else:
                    # 使用AKShare获取数据（备用）
                    import akshare as ak
                    df = ak.stock_zh_a_hist(
                        symbol=symbol,
                        period="daily",
                        start_date=start_date,
                        end_date=end_date,
                        adjust="qfq"
                    )

                    # 规范列名
                    df = df.rename(columns={
                        '日期': 'date',
                        '开盘': 'open',
                        '收盘': 'close',
                        '最高': 'high',
                        '最低': 'low',
                        '成交量': 'volume'
                    })

                    # 转换日期格式
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.sort_values('date').reset_index(drop=True)

                if df.empty:
                    logger.warning(f"股票 {symbol} 无数据")
                    return None

                # 保存到缓存
                self._save_to_cache(df, symbol, start_date, end_date)

                # 请求间隔
                time.sleep(self.config.request_delay)

                return df

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"获取 {symbol} 数据失败 (尝试 {attempt + 1}): {e}")

                if attempt < self.config.retry_count - 1:
                    # 如果是速率限制错误，等待更长时间
                    if "每分钟最多访问" in error_msg or "rate limit" in error_msg.lower():
                        wait_time = 10 + attempt * 5  # 递增等待时间
                        logger.info(f"检测到速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        time.sleep(2)  # 其他错误等待较短时间
                else:
                    logger.error(f"获取 {symbol} 数据最终失败")
                    return None
    
    def get_batch_stock_data(self, stock_symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """
        批量获取股票数据
        
        Args:
            stock_symbols: 股票代码列表
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            {symbol: DataFrame} 字典
        """
        logger.info(f"开始批量获取 {len(stock_symbols)} 只股票数据")
        
        results = {}
        failed_symbols = []
        
        # 使用线程池并发获取
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有任务
            future_to_symbol = {
                executor.submit(self.get_single_stock_data, symbol, start_date, end_date): symbol
                for symbol in stock_symbols
            }
            
            # 收集结果
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        results[symbol] = df
                        logger.info(f"成功获取 {symbol} 数据 ({len(df)} 条记录)")
                    else:
                        failed_symbols.append(symbol)
                        logger.warning(f"股票 {symbol} 数据为空")
                except Exception as e:
                    failed_symbols.append(symbol)
                    logger.error(f"获取 {symbol} 数据异常: {e}")
        
        logger.info(f"批量获取完成: 成功 {len(results)} 只，失败 {len(failed_symbols)} 只")
        
        if failed_symbols:
            logger.warning(f"失败的股票: {failed_symbols}")
        
        return results
    
    def convert_to_stock_data_batch(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, List[StockData]]:
        """批量转换DataFrame为StockData对象"""
        logger.info(f"开始批量转换 {len(data_dict)} 只股票数据")
        
        stock_data_dict = {}
        
        for symbol, df in data_dict.items():
            try:
                stock_data_list = []
                for _, row in df.iterrows():
                    stock_data = StockData(
                        symbol=symbol,
                        date=row['date'].to_pydatetime(),
                        open=Decimal(str(row['open'])),
                        high=Decimal(str(row['high'])),
                        low=Decimal(str(row['low'])),
                        close=Decimal(str(row['close'])),
                        volume=int(row['volume'])
                    )
                    stock_data_list.append(stock_data)
                
                stock_data_dict[symbol] = stock_data_list
                logger.debug(f"转换 {symbol}: {len(stock_data_list)} 条记录")
                
            except Exception as e:
                logger.error(f"转换 {symbol} 数据失败: {e}")
        
        logger.info(f"批量转换完成: {len(stock_data_dict)} 只股票")
        return stock_data_dict
    
    def get_stock_pool_data(self, stock_pool: List[StockInfo], start_date: str, end_date: str) -> Dict[str, List[StockData]]:
        """
        获取股票池的历史数据
        
        Args:
            stock_pool: 股票池信息列表
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            {symbol: List[StockData]} 字典
        """
        symbols = [stock.symbol for stock in stock_pool]
        
        # 批量获取DataFrame数据
        df_dict = self.get_batch_stock_data(symbols, start_date, end_date)
        
        # 转换为StockData对象
        stock_data_dict = self.convert_to_stock_data_batch(df_dict)
        
        return stock_data_dict
    
    def clear_cache(self):
        """清理缓存"""
        if not self.config.cache_enabled:
            return
            
        try:
            import shutil
            if os.path.exists(self.config.cache_dir):
                shutil.rmtree(self.config.cache_dir)
                os.makedirs(self.config.cache_dir, exist_ok=True)
                logger.info("缓存已清理")
        except Exception as e:
            logger.error(f"清理缓存失败: {e}")

def get_stock_pool_with_data(target_count: int = 100, 
                           start_date: str = None, 
                           end_date: str = None) -> Tuple[List[StockInfo], Dict[str, List[StockData]]]:
    """
    一站式获取股票池和历史数据
    
    Args:
        target_count: 目标股票数量
        start_date: 开始日期，默认为1年前
        end_date: 结束日期，默认为今天
        
    Returns:
        (股票池信息, 股票历史数据字典)
    """
    # 设置默认日期
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
    
    logger.info(f"获取股票池和历史数据: {start_date} 到 {end_date}")
    
    # 1. 创建股票池
    from stock_pool import create_default_stock_pool
    stock_pool = create_default_stock_pool(target_count)
    
    # 2. 获取历史数据
    data_loader = BatchDataLoader()
    stock_data_dict = data_loader.get_stock_pool_data(stock_pool, start_date, end_date)
    
    return stock_pool, stock_data_dict

if __name__ == "__main__":
    # 示例使用
    print("开始批量获取股票池数据...")
    
    # 获取50只股票的1年历史数据
    stock_pool, stock_data_dict = get_stock_pool_with_data(
        target_count=50,
        start_date="20240101",
        end_date="20241231"
    )
    
    print(f"\n获取完成:")
    print(f"股票池数量: {len(stock_pool)}")
    print(f"成功获取数据的股票数量: {len(stock_data_dict)}")
    
    # 显示数据统计
    for symbol, data_list in list(stock_data_dict.items())[:5]:  # 显示前5只
        print(f"{symbol}: {len(data_list)} 条记录")
