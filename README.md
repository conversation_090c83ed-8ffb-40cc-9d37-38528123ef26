# 股票分析系统

一个基于ZigZag指标的股票分析系统，支持股票池筛选、批量数据处理、技术分析和策略信号生成。

## 功能特性

### 🎯 核心功能
- **智能股票池筛选**: 基于市值、流动性、估值等多维度筛选优质股票
- **高效ZigZag分析**: 基于ATR动态阈值的ZigZag指标计算，识别趋势转折点
- **批量数据处理**: 支持批量获取和处理多只股票的历史数据
- **数据库存储**: 使用DuckDB高效存储股票数据和分析结果
- **策略接口**: 可扩展的策略框架，支持自定义交易策略
- **可视化分析**: 专业的K线图表和ZigZag标记

### 📊 技术指标
- **ZigZag指标**: 基于ATR动态阈值，自动识别价格趋势转折点
- **趋势分析**: 实时跟踪股票趋势状态（上升/下降）
- **拐点识别**: 精确标记高点和低点，过滤市场噪音

### 🔧 系统架构
- **模块化设计**: 各功能模块独立，易于维护和扩展
- **数据缓存**: 智能缓存机制，提高数据获取效率
- **并发处理**: 多线程批量处理，提升系统性能
- **错误处理**: 完善的异常处理和日志记录

## 项目结构

```
stock/
├── main.py                     # 主入口文件
├── data_models.py              # 数据模型定义
├── stock_pool.py               # 股票池筛选模块
├── batch_data_loader.py        # 批量数据获取模块
├── zigzag.py                   # ZigZag指标计算（原有）
├── zigzag_analysis.py          # ZigZag分析工具（原有）
├── batch_zigzag_processor.py   # 批量ZigZag处理器
├── database_schema.py          # 数据库表结构和管理
├── strategy_interface.py       # 策略接口框架
├── stock_visualization.py      # 可视化模块（原有）
└── pyproject.toml              # 项目配置
```

## 快速开始

### 安装依赖

```bash
# 使用uv安装依赖
uv sync

# 或使用pip
pip install tushare akshare pyecharts duckdb pandas
```

### 配置数据源

系统支持两种数据源：**Tushare**（推荐）和 **AKShare**（备用）

#### 配置Tushare（推荐）
1. 访问 [https://tushare.pro/](https://tushare.pro/) 注册获取免费token
2. 配置token：
```bash
# 方法1: 交互式配置
python main.py --mode config

# 方法2: 环境变量
set TUSHARE_TOKEN=你的token

# 方法3: 配置文件
echo "你的token" > tushare_token.txt
```

#### Tushare优势
- ✅ 更稳定的数据源
- ✅ 更丰富的数据字段
- ✅ 更好的数据质量
- ✅ 支持更多技术指标

### 基本使用

#### 1. 配置数据源
```bash
# 配置Tushare token
python main.py --mode config

# 测试Tushare功能
python tushare_demo.py
```

#### 2. 完整分析流程
```bash
# 使用Tushare数据源（推荐）
python main.py --mode full --count 100 --days 365 --data-source tushare

# 使用AKShare数据源（备用）
python main.py --mode full --count 50 --days 180 --data-source akshare

# 指定Tushare token
python main.py --mode full --token 你的token
```

#### 3. 更新现有数据
```bash
# 更新所有股票数据
python main.py --mode update --data-source tushare

# 更新指定股票
python main.py --mode update --symbols 000001 000002 600000
```

#### 4. 策略分析
```bash
# 运行策略分析
python main.py --mode strategy

# 对指定股票运行策略
python main.py --mode strategy --symbols 000001 600000
```

#### 5. 演示脚本
```bash
# Tushare功能演示
python tushare_demo.py

# 离线功能演示
python offline_demo.py
```

### 编程接口使用

#### 股票池筛选
```python
from stock_pool import StockPoolSelector, StockPoolConfig

# 创建筛选配置
config = StockPoolConfig(
    min_market_cap=100.0,  # 最小市值100亿
    max_market_cap=3000.0, # 最大市值3000亿
    min_price=10.0,        # 最小价格10元
    max_price=150.0,       # 最大价格150元
)

# 筛选股票池
selector = StockPoolSelector(config)
stock_pool = selector.select_stock_pool(100)
```

#### 批量数据获取
```python
from batch_data_loader import BatchDataLoader

# 创建数据加载器
loader = BatchDataLoader()

# 批量获取数据
symbols = ['000001', '000002', '600000']
data_dict = loader.get_batch_stock_data(symbols, '20240101', '20241231')
```

#### ZigZag分析
```python
from batch_zigzag_processor import IntegratedStockAnalyzer

# 创建分析器
analyzer = IntegratedStockAnalyzer()
analyzer.initialize_system()

# 运行完整分析
results = analyzer.create_and_analyze_stock_pool(
    target_count=50,
    start_date='20240101',
    end_date='20241231'
)
```

#### 策略开发
```python
from strategy_interface import BaseStrategy, Signal, StrategyConfig

class MyStrategy(BaseStrategy):
    def analyze(self, stock_data, zigzag_state, context=None):
        # 实现你的策略逻辑
        signals = []
        # ... 分析逻辑
        return signals

    def get_strategy_info(self):
        return {
            'name': '我的策略',
            'description': '策略描述',
            'type': 'custom'
        }
```

## 数据库结构

系统使用DuckDB存储以下数据：

- **stock_info**: 股票基本信息
- **stock_daily_data**: 股票历史数据
- **zigzag_states**: ZigZag计算状态
- **zigzag_points**: ZigZag拐点数据
- **technical_indicators**: 技术指标数据
- **strategy_signals**: 策略信号
- **backtest_results**: 回测结果