"""
Tushare配置管理模块
"""

import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class TushareConfig:
    """Tushare配置管理"""
    
    def __init__(self):
        self.token = None
        self._load_token()
    
    def _load_token(self):
        """从环境变量或配置文件加载token"""
        # 1. 尝试从环境变量获取
        self.token = os.getenv('TUSHARE_TOKEN')
        
        if self.token:
            logger.info("从环境变量加载Tushare token")
            return
        
        # 2. 尝试从配置文件获取
        config_files = ['tushare_token.txt', '.tushare_token', 'config/tushare_token.txt']
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        self.token = f.read().strip()
                    if self.token:
                        logger.info(f"从配置文件 {config_file} 加载Tushare token")
                        return
                except Exception as e:
                    logger.warning(f"读取配置文件 {config_file} 失败: {e}")
        
        # 3. 如果都没有找到，给出提示
        if not self.token:
            logger.warning("未找到Tushare token，请设置环境变量TUSHARE_TOKEN或创建配置文件")
    
    def set_token(self, token: str):
        """设置token"""
        self.token = token
        logger.info("手动设置Tushare token")
    
    def save_token_to_file(self, filename: str = 'tushare_token.txt'):
        """保存token到文件"""
        if not self.token:
            raise ValueError("没有可保存的token")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.token)
            logger.info(f"Token已保存到 {filename}")
        except Exception as e:
            logger.error(f"保存token失败: {e}")
            raise
    
    def get_token(self) -> Optional[str]:
        """获取token"""
        return self.token
    
    def is_configured(self) -> bool:
        """检查是否已配置token"""
        return bool(self.token)

# 全局配置实例
_tushare_config = None

def get_tushare_config() -> TushareConfig:
    """获取Tushare配置单例"""
    global _tushare_config
    if _tushare_config is None:
        _tushare_config = TushareConfig()
    return _tushare_config

def setup_tushare_token(token: str = None) -> bool:
    """
    设置Tushare token
    
    Args:
        token: Tushare API token，如果为None则尝试从环境变量或文件获取
        
    Returns:
        是否设置成功
    """
    config = get_tushare_config()
    
    if token:
        config.set_token(token)
        return True
    
    if config.is_configured():
        logger.info("Tushare token已配置")
        return True
    
    # 交互式设置token
    print("\n" + "="*60)
    print("Tushare配置")
    print("="*60)
    print("未找到Tushare API token。")
    print("请访问 https://tushare.pro/ 注册并获取免费token。")
    print("="*60)
    
    try:
        user_token = input("请输入您的Tushare token (或按Enter跳过): ").strip()
        
        if user_token:
            config.set_token(user_token)
            
            # 询问是否保存
            save_choice = input("是否保存token到本地文件? (y/N): ").strip().lower()
            if save_choice in ['y', 'yes']:
                config.save_token_to_file()
                print("Token已保存到 tushare_token.txt")
            
            return True
        else:
            print("跳过Tushare配置，将使用AKShare作为备用数据源")
            return False
            
    except KeyboardInterrupt:
        print("\n配置被取消")
        return False
    except Exception as e:
        logger.error(f"配置token失败: {e}")
        return False

def create_tushare_config_template():
    """创建Tushare配置模板文件"""
    template_content = """# Tushare配置说明

## 获取Token
1. 访问 https://tushare.pro/
2. 注册账号（免费）
3. 在个人中心获取token

## 配置方法

### 方法1: 环境变量
设置环境变量 TUSHARE_TOKEN=你的token

### 方法2: 配置文件
创建文件 tushare_token.txt，内容为你的token

### 方法3: 代码设置
```python
from tushare_config import setup_tushare_token
setup_tushare_token("你的token")
```

## 注意事项
- 免费用户有调用频率限制
- 建议设置合理的请求间隔
- Token请妥善保管，不要泄露
"""
    
    try:
        with open('TUSHARE_SETUP.md', 'w', encoding='utf-8') as f:
            f.write(template_content)
        print("已创建Tushare配置说明文件: TUSHARE_SETUP.md")
    except Exception as e:
        logger.error(f"创建配置模板失败: {e}")

if __name__ == "__main__":
    # 测试配置
    print("测试Tushare配置...")
    
    config = get_tushare_config()
    
    if config.is_configured():
        print(f"Token已配置: {config.get_token()[:10]}...")
    else:
        print("Token未配置")
        create_tushare_config_template()
        
        # 尝试设置token
        success = setup_tushare_token()
        if success:
            print("Token配置成功")
        else:
            print("Token配置失败或跳过")
