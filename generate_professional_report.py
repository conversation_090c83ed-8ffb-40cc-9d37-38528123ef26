"""
专业股票突破分析报告生成器
生成详尽的专业分析报告
"""

from simple_breakthrough_analysis import SimpleBreakthroughAnalyzer
from batch_data_loader import BatchDataConfig
from tushare_config import get_tushare_config
from datetime import datetime
import json

def generate_comprehensive_report():
    """生成综合分析报告"""
    print("🎯 专业股票突破分析报告生成器")
    print("="*60)
    
    # 股票列表 - 可以根据需要修改
    stock_symbols = [
        '000001',  # 平安银行
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415',  # 海康威视
        '600276',  # 恒瑞医药
        '000725',  # 京东方A
        '002594',  # 比亚迪
        '600887',  # 伊利股份
        '000002'   # 万科A
    ]
    
    print(f"分析股票列表: {', '.join(stock_symbols)}")
    print(f"股票数量: {len(stock_symbols)} 只")
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,  # 保守设置避免速率限制
        request_delay=1.2  # 增加请求间隔
    )
    
    print(f"数据源: {data_config.data_source}")
    print("请耐心等待，正在获取和分析数据...")
    print()
    
    # 创建分析器
    analyzer = SimpleBreakthroughAnalyzer(data_config)
    
    # 执行分析
    try:
        results = analyzer.analyze_stocks(stock_symbols, years=2)
        
        print("\n" + "="*60)
        print("📊 分析完成，正在生成报告...")
        print("="*60)
        
        # 生成详细报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"专业突破分析报告_{timestamp}.md"
        
        report_content = analyzer.generate_detailed_report(results, report_file)
        
        # 生成JSON数据文件
        json_file = f"分析数据_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 显示结果摘要
        print(f"✅ 分析完成！")
        print(f"📄 详细报告: {report_file}")
        print(f"📊 数据文件: {json_file}")
        print()
        
        # 显示核心发现
        breakthrough_count = len(results['stocks_with_breakthrough'])
        if breakthrough_count > 0:
            print("🎯 核心发现:")
            print(f"   发现 {breakthrough_count} 只突破股票")
            
            # 显示前3只最强的股票
            top_stocks = sorted(results['stocks_with_breakthrough'], 
                              key=lambda x: x['breakthrough_strength'], reverse=True)[:3]
            
            for i, stock in enumerate(top_stocks, 1):
                strength = stock['breakthrough_strength']
                symbol = stock['symbol']
                change = stock['price_change_30d']
                
                if strength >= 80:
                    rating = "⭐⭐⭐⭐⭐ 强烈推荐"
                elif strength >= 70:
                    rating = "⭐⭐⭐⭐ 推荐"
                elif strength >= 60:
                    rating = "⭐⭐⭐ 关注"
                else:
                    rating = "⭐⭐ 观望"
                
                print(f"   {i}. {symbol} - {rating}")
                print(f"      突破强度: {strength:.0f}/100")
                print(f"      30天涨幅: {change:+.1f}%")
        else:
            print("📋 分析结果:")
            print("   未发现明显突破模式")
            print("   建议继续观察或扩大分析范围")
        
        print()
        print("💡 使用建议:")
        print("   1. 查看详细报告了解完整分析")
        print("   2. 结合基本面分析做最终决策")
        print("   3. 注意风险控制和资金管理")
        print("   4. 定期更新分析保持时效性")
        
        return results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def custom_stock_analysis():
    """自定义股票分析"""
    print("\n" + "="*60)
    print("🎯 自定义股票分析")
    print("="*60)
    
    print("请输入要分析的股票代码（用空格分隔）:")
    print("例如: 000001 600036 600519 000858")
    user_input = input("股票代码: ").strip()
    
    if not user_input:
        print("未输入股票代码，使用默认分析")
        return generate_comprehensive_report()
    
    custom_stocks = user_input.split()
    print(f"将分析: {', '.join(custom_stocks)}")
    
    # 询问分析年数
    try:
        years = int(input("分析年数 (1-5，默认2): ") or "2")
        years = max(1, min(5, years))
    except:
        years = 2
    
    print(f"分析 {years} 年数据...")
    
    # 配置和执行分析
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.2
    )
    
    analyzer = SimpleBreakthroughAnalyzer(data_config)
    
    try:
        results = analyzer.analyze_stocks(custom_stocks, years=years)
        
        # 生成报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"自定义分析报告_{timestamp}.md"
        analyzer.generate_detailed_report(results, report_file)
        
        print(f"✅ 自定义分析完成！")
        print(f"📄 报告文件: {report_file}")
        
        return results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def main():
    """主菜单"""
    print("🎯 专业股票突破分析报告生成器")
    print("="*50)
    print("1. 综合分析报告 (10只热门股票)")
    print("2. 自定义股票分析")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        return generate_comprehensive_report()
    elif choice == "2":
        return custom_stock_analysis()
    elif choice == "3":
        print("再见！")
        return None
    else:
        print("无效选择，执行综合分析...")
        return generate_comprehensive_report()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        print("请检查网络连接和数据源配置")
