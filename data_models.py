from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from decimal import Decimal

@dataclass
class StockData:
    """股票数据模型"""
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int

    # 技术指标字段
    macd: Optional[Decimal] = None
    macd_signal: Optional[Decimal] = None
    macd_histogram: Optional[Decimal] = None
    bb_upper: Optional[Decimal] = None
    bb_middle: Optional[Decimal] = None
    bb_lower: Optional[Decimal] = None

    # ZigZag 和趋势字段
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None  # 'HIGH' or 'LOW'
    trend_status: Optional[str] = None       # 'UP' or 'DOWN'

@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    date: datetime
    signal_type: str  # 'BUY' or 'SELL'
    price: Decimal
    reason: str  # 信号原因
    confidence: float  # 信号置信度 0-1